#!/bin/bash

# =============================================================================
# Nirvana Organics - Unified Deployment Script
# =============================================================================
# 
# Comprehensive deployment solution for Nirvana Organics e-commerce platform
# Supports both test and production environments on fresh Hostinger VPS
# 
# Author: Nirvana Organics Development Team
# Version: 2.0.0
# Date: 2025-01-01
# 
# Usage:
#   sudo ./deploy-nirvana-organics.sh [test|production]
# 
# Examples:
#   sudo ./deploy-nirvana-organics.sh test
#   sudo ./deploy-nirvana-organics.sh production
# 
# =============================================================================

set -e  # Exit on any error
set -u  # Exit on undefined variables

# =============================================================================
# SCRIPT CONFIGURATION
# =============================================================================

SCRIPT_VERSION="2.0.0"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOYMENT_DATE=$(date '+%Y-%m-%d %H:%M:%S')
LOG_TIMESTAMP=$(date '+%Y%m%d-%H%M%S')

# Default environment (can be overridden by command line argument)
ENVIRONMENT="${1:-}"

# Validate environment argument
if [[ -z "$ENVIRONMENT" ]]; then
    echo "❌ Error: Environment not specified"
    echo "Usage: sudo $0 [test|production]"
    echo "Examples:"
    echo "  sudo $0 test"
    echo "  sudo $0 production"
    exit 1
fi

if [[ "$ENVIRONMENT" != "test" && "$ENVIRONMENT" != "production" ]]; then
    echo "❌ Error: Invalid environment '$ENVIRONMENT'"
    echo "Valid environments: test, production"
    exit 1
fi

# =============================================================================
# ENVIRONMENT-SPECIFIC CONFIGURATION
# =============================================================================

if [[ "$ENVIRONMENT" == "test" ]]; then
    # Test Environment Configuration
    DOMAIN="test.shopnirvanaorganics.com"
    DEPLOY_USER="Nirvana"
    BACKEND_PATH="/var/www/nirvana-test/server"
    FRONTEND_MAIN_PATH="/var/www/nirvana-test/main"
    FRONTEND_ADMIN_PATH="/var/www/nirvana-test/admin"
    NGINX_SITE_NAME="nirvana-organics-test"
    DB_NAME="u106832845_nirvana"
    MAIN_PORT="5000"
    ADMIN_PORT="3001"
    ENV_FILE=".env.test"
    ADMIN_ENV_FILE=".env.admin.test"
    FRONTEND_ENV_FILE=".env.frontend.test"
    ADMIN_FRONTEND_ENV_FILE=".env.admin.frontend.test"
    PM2_MAIN_APP_NAME="nirvana-backend-main-test"
    PM2_ADMIN_APP_NAME="nirvana-backend-admin-test"
    BACKUP_PATH="/var/backups/nirvana-organics-test"
    LOG_PATH="/var/log/nirvana-test"
    RATE_LIMIT_REQUESTS="1000"
    ADMIN_RATE_LIMIT_REQUESTS="200"
    SSL_EMAIL="<EMAIL>"
    
elif [[ "$ENVIRONMENT" == "production" ]]; then
    # Production Environment Configuration
    DOMAIN="shopnirvanaorganics.com"
    DEPLOY_USER="Nirvana"
    BACKEND_PATH="/var/www/nirvana-production/server"
    FRONTEND_MAIN_PATH="/var/www/nirvana-production/main"
    FRONTEND_ADMIN_PATH="/var/www/nirvana-production/admin"
    NGINX_SITE_NAME="nirvana-organics-production"
    DB_NAME="u106832845_nirvana"
    MAIN_PORT="5000"
    ADMIN_PORT="3001"
    ENV_FILE=".env.production"
    ADMIN_ENV_FILE=".env.admin.production"
    FRONTEND_ENV_FILE=".env.frontend.production"
    ADMIN_FRONTEND_ENV_FILE=".env.admin.frontend.production"
    PM2_MAIN_APP_NAME="nirvana-backend-main-production"
    PM2_ADMIN_APP_NAME="nirvana-backend-admin-production"
    BACKUP_PATH="/var/backups/nirvana-organics-production"
    LOG_PATH="/var/log/nirvana-production"
    RATE_LIMIT_REQUESTS="500"
    ADMIN_RATE_LIMIT_REQUESTS="100"
    SSL_EMAIL="<EMAIL>"
fi

# Common Configuration
LOG_FILE="$LOG_PATH/deployment-$LOG_TIMESTAMP.log"
SITES_AVAILABLE="/etc/nginx/sites-available"
SITES_ENABLED="/etc/nginx/sites-enabled"
MYSQL_ROOT_PASSWORD=""  # Will be set during installation
NODE_VERSION="18"

# =============================================================================
# COLORS AND LOGGING
# =============================================================================

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
BOLD='\033[1m'
NC='\033[0m'

# Logging functions
log() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${BLUE}[${timestamp}]${NC} $message" | tee -a "$LOG_FILE"
}

error() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${RED}❌ [ERROR ${timestamp}]${NC} $message" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${GREEN}✅ [SUCCESS ${timestamp}]${NC} $message" | tee -a "$LOG_FILE"
}

warning() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${YELLOW}⚠️  [WARNING ${timestamp}]${NC} $message" | tee -a "$LOG_FILE"
}

info() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${CYAN}ℹ️  [INFO ${timestamp}]${NC} $message" | tee -a "$LOG_FILE"
}

step() {
    local message="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "\n${WHITE}${BOLD}🔧 [$timestamp] $message${NC}" | tee -a "$LOG_FILE"
    echo -e "${WHITE}$(printf '=%.0s' {1..80})${NC}" | tee -a "$LOG_FILE"
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

check_root() {
    if [[ $EUID -ne 0 ]]; then
        error "This script must be run as root (use sudo)"
    fi
}

check_os() {
    if [[ ! -f /etc/os-release ]]; then
        error "Cannot determine operating system"
    fi
    
    source /etc/os-release
    if [[ "$ID" != "ubuntu" && "$ID" != "debian" ]]; then
        error "This script only supports Ubuntu and Debian systems"
    fi
    
    success "Operating System: $PRETTY_NAME"
}

create_log_directory() {
    mkdir -p "$LOG_PATH"
    chmod 755 "$LOG_PATH"
    touch "$LOG_FILE"
    chmod 644 "$LOG_FILE"
}

create_deploy_user() {
    step "Creating Deployment User"
    
    if id "$DEPLOY_USER" &>/dev/null; then
        info "User '$DEPLOY_USER' already exists"
    else
        log "Creating user '$DEPLOY_USER'..."
        useradd -m -s /bin/bash "$DEPLOY_USER"
        usermod -aG sudo "$DEPLOY_USER"
        success "User '$DEPLOY_USER' created successfully"
    fi
    
    # Create SSH directory for the user
    sudo -u "$DEPLOY_USER" mkdir -p "/home/<USER>/.ssh"
    sudo -u "$DEPLOY_USER" chmod 700 "/home/<USER>/.ssh"
    
    # Set up basic directories
    sudo -u "$DEPLOY_USER" mkdir -p "/home/<USER>/.pm2/logs"
    sudo -u "$DEPLOY_USER" mkdir -p "/home/<USER>/.config"
    
    success "Deployment user setup completed"
}

update_system() {
    step "Updating System Packages"
    
    log "Updating package lists..."
    apt-get update -y
    
    log "Upgrading system packages..."
    apt-get upgrade -y
    
    log "Installing essential packages..."
    apt-get install -y \
        curl \
        wget \
        git \
        unzip \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        build-essential \
        python3 \
        python3-pip \
        certbot \
        python3-certbot-nginx
    
    success "System packages updated successfully"
}

install_nodejs() {
    step "Installing Node.js"
    
    if command -v node &> /dev/null; then
        local current_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [[ "$current_version" -ge "$NODE_VERSION" ]]; then
            success "Node.js $current_version is already installed"
            return
        fi
    fi
    
    log "Installing Node.js $NODE_VERSION..."
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | bash -
    apt-get install -y nodejs
    
    # Verify installation
    local installed_version=$(node --version)
    local npm_version=$(npm --version)
    
    success "Node.js $installed_version installed successfully"
    success "npm $npm_version installed successfully"
}

install_pm2() {
    step "Installing PM2 Process Manager"
    
    if command -v pm2 &> /dev/null; then
        success "PM2 is already installed"
        return
    fi
    
    log "Installing PM2 globally..."
    npm install -g pm2
    
    # Set up PM2 startup script
    log "Configuring PM2 startup script..."
    pm2 startup systemd -u "$DEPLOY_USER" --hp "/home/<USER>"
    
    success "PM2 installed and configured successfully"
}

install_nginx() {
    step "Installing Nginx Web Server"
    
    if command -v nginx &> /dev/null; then
        success "Nginx is already installed"
        return
    fi
    
    log "Installing Nginx..."
    apt-get install -y nginx
    
    log "Starting and enabling Nginx..."
    systemctl start nginx
    systemctl enable nginx
    
    # Configure firewall
    log "Configuring firewall for Nginx..."
    ufw allow 'Nginx Full'
    
    success "Nginx installed and configured successfully"
}

install_mysql() {
    step "Installing MySQL/MariaDB Database Server"
    
    if command -v mysql &> /dev/null; then
        success "MySQL is already installed"
        return
    fi
    
    log "Installing MariaDB server..."
    apt-get install -y mariadb-server mariadb-client
    
    log "Starting and enabling MariaDB..."
    systemctl start mariadb
    systemctl enable mariadb
    
    log "Securing MariaDB installation..."
    # Set root password and secure installation
    mysql -e "UPDATE mysql.user SET Password = PASSWORD('$MYSQL_ROOT_PASSWORD') WHERE User = 'root'"
    mysql -e "DELETE FROM mysql.user WHERE User=''"
    mysql -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1')"
    mysql -e "DROP DATABASE IF EXISTS test"
    mysql -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%'"
    mysql -e "FLUSH PRIVILEGES"
    
    success "MariaDB installed and secured successfully"
}

install_redis() {
    step "Installing Redis Cache Server"

    if command -v redis-server &> /dev/null; then
        success "Redis is already installed"
        return
    fi

    log "Installing Redis..."
    apt-get install -y redis-server

    log "Configuring Redis..."
    sed -i 's/^supervised no/supervised systemd/' /etc/redis/redis.conf

    log "Starting and enabling Redis..."
    systemctl restart redis-server
    systemctl enable redis-server

    success "Redis installed and configured successfully"
}

create_directory_structure() {
    step "Creating Directory Structure"

    log "Creating backend directory: $BACKEND_PATH"
    mkdir -p "$BACKEND_PATH"

    log "Creating frontend directories..."
    mkdir -p "$FRONTEND_MAIN_PATH"
    mkdir -p "$FRONTEND_ADMIN_PATH"

    log "Creating backup directory: $BACKUP_PATH"
    mkdir -p "$BACKUP_PATH"

    log "Creating upload directories..."
    mkdir -p "$BACKEND_PATH/../uploads/products"
    mkdir -p "$BACKEND_PATH/../uploads/categories"
    mkdir -p "$BACKEND_PATH/../uploads/banners"
    mkdir -p "$BACKEND_PATH/../uploads/documents"

    log "Setting directory ownership..."
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$(dirname "$BACKEND_PATH")"
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKUP_PATH"

    log "Setting directory permissions..."
    find "$(dirname "$BACKEND_PATH")" -type d -exec chmod 755 {} \;
    find "$(dirname "$BACKEND_PATH")" -type f -exec chmod 644 {} \;

    success "Directory structure created successfully"
}

setup_ssl_certificates() {
    step "Setting up SSL Certificates"

    log "Checking if SSL certificate exists for $DOMAIN..."
    if [[ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]]; then
        success "SSL certificate already exists for $DOMAIN"
        return
    fi

    log "Obtaining SSL certificate for $DOMAIN..."
    certbot --nginx -d "$DOMAIN" --non-interactive --agree-tos --email "$SSL_EMAIL"

    if [[ -f "/etc/letsencrypt/live/$DOMAIN/fullchain.pem" ]]; then
        success "SSL certificate obtained successfully for $DOMAIN"

        # Set up automatic renewal
        log "Setting up automatic SSL renewal..."
        (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
        success "SSL automatic renewal configured"
    else
        warning "SSL certificate setup failed - continuing with HTTP configuration"
    fi
}

deploy_backend_code() {
    step "Deploying Backend Code"

    log "Copying server files to $BACKEND_PATH..."
    if [[ -d "$SCRIPT_DIR/server" ]]; then
        cp -r "$SCRIPT_DIR/server"/* "$BACKEND_PATH/"
    else
        error "Server directory not found in $SCRIPT_DIR/server"
    fi

    log "Setting backend file ownership..."
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$BACKEND_PATH"

    log "Installing backend dependencies..."
    cd "$BACKEND_PATH"
    sudo -u "$DEPLOY_USER" npm install --production

    success "Backend code deployed successfully"
}

create_environment_files() {
    step "Creating Environment Configuration Files"

    local backend_env_path="$(dirname "$BACKEND_PATH")/$ENV_FILE"
    local admin_env_path="$(dirname "$BACKEND_PATH")/$ADMIN_ENV_FILE"

    log "Creating main backend environment file: $backend_env_path"
    create_main_env_file "$backend_env_path"

    log "Creating admin backend environment file: $admin_env_path"
    create_admin_env_file "$admin_env_path"

    log "Setting environment file permissions..."
    chmod 600 "$backend_env_path"
    chmod 600 "$admin_env_path"
    chown "$DEPLOY_USER:$DEPLOY_USER" "$backend_env_path"
    chown "$DEPLOY_USER:$DEPLOY_USER" "$admin_env_path"

    success "Environment files created successfully"
}

create_main_env_file() {
    local env_file="$1"

    cat > "$env_file" << EOF
# Nirvana Organics - $ENVIRONMENT Environment Configuration
# Main Server Configuration
# Generated on: $DEPLOYMENT_DATE

# Environment
NODE_ENV=$ENVIRONMENT

# Server Configuration
PORT=$MAIN_PORT
FRONTEND_URL=https://$DOMAIN
BACKEND_URL=https://$DOMAIN

# Database Configuration
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=$DB_NAME
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_SSL=true
DB_POOL_MAX=10
DB_POOL_MIN=2

# CORS Configuration
CORS_ORIGIN=https://$DOMAIN

# Security Configuration
JWT_SECRET=4d18f78eb794845484ea3fa05759c0e4412b0987c054ce0e2009050f3ef4fefc
JWT_REFRESH_SECRET=zvBFWhW0eP3uNAp0t0KLIRq0owdq1QX82OmLLXyMpSY=
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
MAIN_RATE_LIMIT_MAX_REQUESTS=$RATE_LIMIT_REQUESTS

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics

# Payment Configuration (Square)
SQUARE_ENVIRONMENT=$([[ "$ENVIRONMENT" == "production" ]] && echo "production" || echo "sandbox")
SQUARE_APPLICATION_ID=$([[ "$ENVIRONMENT" == "production" ]] && echo "your-production-square-app-id" || echo "*************************************")
SQUARE_ACCESS_TOKEN=$([[ "$ENVIRONMENT" == "production" ]] && echo "your-production-square-access-token" || echo "****************************************************************")
SQUARE_WEBHOOK_SIGNATURE_KEY=$([[ "$ENVIRONMENT" == "production" ]] && echo "your-production-webhook-key" || echo "ss2HK0tSkM5sm_e3qR42Dg")
SQUARE_LOCATION_ID=$([[ "$ENVIRONMENT" == "production" ]] && echo "your-production-location-id" || echo "LWBM41BAWMRQS")

# Google OAuth
GOOGLE_CLIENT_ID=53561266132-fn7bptsn5hr9jpfim2hp77845fjtl6cn.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_OAUTH_CALLBACK_URL=https://$DOMAIN/api/auth/google/callback

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Logging Configuration
LOG_LEVEL=$([[ "$ENVIRONMENT" == "production" ]] && echo "info" || echo "debug")
LOG_FILE=$LOG_PATH/main.log
ERROR_LOG_FILE=$LOG_PATH/main-error.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=1800

# Security Headers
MAIN_SECURITY_MODE=true
ENABLE_HTTPS_REDIRECT=true

# Session Configuration
SESSION_SECRET=your-secure-session-secret-key-here
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict

# API Keys
ENCRYPTION_KEY=744c05e48632e7a4aaa86ca5f85e733fff59dfcfc37bf5d43a72e8462df0f89d
API_KEY_SECRET=slji4ImLCC4EhyyEYZPT49T2EZAGmg83gBJae0Rkbeg=

# Monitoring
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITORING=true

# Debug Configuration
DEBUG_MODE=$([[ "$ENVIRONMENT" == "production" ]] && echo "false" || echo "true")
ENABLE_CORS_DEBUG=$([[ "$ENVIRONMENT" == "production" ]] && echo "false" || echo "true")
SQL_LOGGING=$([[ "$ENVIRONMENT" == "production" ]] && echo "false" || echo "true")

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 3 * * *
BACKUP_RETENTION_DAYS=$([[ "$ENVIRONMENT" == "production" ]] && echo "30" || echo "7")
EOF
}

create_admin_env_file() {
    local env_file="$1"

    cat > "$env_file" << EOF
# Nirvana Organics - $ENVIRONMENT Admin Environment Configuration
# Admin Server Configuration
# Generated on: $DEPLOYMENT_DATE

# Environment
NODE_ENV=$ENVIRONMENT

# Server Configuration
PORT=$ADMIN_PORT
FRONTEND_URL=https://$DOMAIN/admin
BACKEND_URL=https://$DOMAIN

# Database Configuration
DB_HOST=srv1921.hstgr.io
DB_PORT=3306
DB_NAME=$DB_NAME
DB_USER=u106832845_root
DB_PASSWORD=Hrishikesh@0912
DB_SSL=true
DB_POOL_MAX=6
DB_POOL_MIN=2

# CORS Configuration (Admin specific)
CORS_ORIGIN=https://$DOMAIN

# Security Configuration (Enhanced for admin)
JWT_SECRET=4d18f78eb794845484ea3fa05759c0e4412b0987c054ce0e2009050f3ef4fefc
JWT_REFRESH_SECRET=zvBFWhW0eP3uNAp0t0KLIRq0owdq1QX82OmLLXyMpSY=
JWT_EXPIRES_IN=30m
JWT_REFRESH_EXPIRES_IN=2h
BCRYPT_ROUNDS=12

# Admin Rate Limiting
RATE_LIMIT_WINDOW_MS=300000
ADMIN_RATE_LIMIT_MAX_REQUESTS=$ADMIN_RATE_LIMIT_REQUESTS

# Admin Security
ADMIN_SECURITY_MODE=true
ADMIN_IP_WHITELIST=

# Email Configuration (Admin)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
SMTP_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=aowu yxxq xeyj qbpe
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Shop Nirvana Organics Admin

# Square OAuth Configuration (Admin only)
SQUARE_OAUTH_CLIENT_ID=$([[ "$ENVIRONMENT" == "production" ]] && echo "your-production-square-oauth-client-id" || echo "*************************************")
SQUARE_OAUTH_CLIENT_SECRET=$([[ "$ENVIRONMENT" == "production" ]] && echo "your-production-square-oauth-client-secret" || echo "sandbox-sq0csb-wPrc4mMkN8ZA2nz5md75DlDNdy2BVUG7_9l9G2zjnzc")
SQUARE_OAUTH_CALLBACK_URL=https://$DOMAIN/admin/api/auth/square/callback

# Google OAuth (Admin)
GOOGLE_CLIENT_ID=53561266132-fn7bptsn5hr9jpfim2hp77845fjtl6cn.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_OAUTH_CALLBACK_URL=https://$DOMAIN/admin/api/auth/google/callback

# File Upload Configuration (Admin)
UPLOAD_DIR=uploads
MAX_FILE_SIZE=50485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xlsx,csv

# Logging Configuration (Admin)
LOG_LEVEL=$([[ "$ENVIRONMENT" == "production" ]] && echo "info" || echo "debug")
LOG_FILE=$LOG_PATH/admin.log
ERROR_LOG_FILE=$LOG_PATH/admin-error.log
AUDIT_LOG_FILE=$LOG_PATH/admin-audit.log

# Cache Configuration
REDIS_URL=redis://localhost:6379
CACHE_TTL=900

# Session Configuration (Admin)
SESSION_SECRET=your-admin-session-secret-key-here
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict
SESSION_TIMEOUT=3600000

# API Keys
ENCRYPTION_KEY=744c05e48632e7a4aaa86ca5f85e733fff59dfcfc37bf5d43a72e8462df0f89d
API_KEY_SECRET=slji4ImLCC4EhyyEYZPT49T2EZAGmg83gBJae0Rkbeg=

# Monitoring
HEALTH_CHECK_INTERVAL=30000
PERFORMANCE_MONITORING=true

# Debug Configuration
DEBUG_MODE=$([[ "$ENVIRONMENT" == "production" ]] && echo "false" || echo "true")
ENABLE_CORS_DEBUG=$([[ "$ENVIRONMENT" == "production" ]] && echo "false" || echo "true")
SQL_LOGGING=$([[ "$ENVIRONMENT" == "production" ]] && echo "false" || echo "true")

# Admin Features
ENABLE_DATA_EXPORT=true
ENABLE_BULK_OPERATIONS=true
ENABLE_SYSTEM_MAINTENANCE=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 4 * * *
BACKUP_RETENTION_DAYS=$([[ "$ENVIRONMENT" == "production" ]] && echo "30" || echo "14")
EOF
}

create_nginx_configuration() {
    step "Creating Nginx Configuration"

    local nginx_config="$SITES_AVAILABLE/$NGINX_SITE_NAME"

    log "Creating Nginx configuration file: $nginx_config"

    cat > "$nginx_config" << EOF
# Nirvana Organics - $ENVIRONMENT Environment Nginx Configuration
# Domain: $DOMAIN
# Generated on: $DEPLOYMENT_DATE

# Rate limiting zones
limit_req_zone \$binary_remote_addr zone=${ENVIRONMENT}_api_limit:10m rate=20r/s;
limit_req_zone \$binary_remote_addr zone=${ENVIRONMENT}_admin_limit:10m rate=10r/s;
limit_req_zone \$binary_remote_addr zone=${ENVIRONMENT}_general_limit:10m rate=50r/s;

# Upstream servers
upstream nirvana_main_backend_${ENVIRONMENT} {
    least_conn;
    server 127.0.0.1:$MAIN_PORT max_fails=3 fail_timeout=30s;
    keepalive 32;
}

upstream nirvana_admin_backend_${ENVIRONMENT} {
    least_conn;
    server 127.0.0.1:$ADMIN_PORT max_fails=3 fail_timeout=30s;
    keepalive 16;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    server_name $DOMAIN;

    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

# Main HTTPS server
server {
    listen 443 ssl http2;
    server_name $DOMAIN;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/$DOMAIN/chain.pem;

    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.squareup.com https://sandbox.web.squarecdn.com https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://connect.squareup.com https://connect.squareupsandbox.com https://www.google-analytics.com; frame-src 'self' https://js.squareup.com https://sandbox.web.squarecdn.com;" always;

    # Environment banner
    add_header X-Environment "$ENVIRONMENT" always;

    # Root directory for static files (main frontend)
    root $FRONTEND_MAIN_PATH;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting
    limit_req zone=${ENVIRONMENT}_general_limit burst=100 nodelay;

    # API routes - proxy to backend
    location /api/ {
        limit_req zone=${ENVIRONMENT}_api_limit burst=50 nodelay;

        proxy_pass http://nirvana_main_backend_${ENVIRONMENT};
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;

        proxy_cache_bypass \$http_upgrade;
        proxy_redirect off;
        proxy_buffering off;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Error handling
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }

    # Admin panel routes - proxy to admin backend
    location /admin/api/ {
        limit_req zone=${ENVIRONMENT}_admin_limit burst=20 nodelay;

        proxy_pass http://nirvana_admin_backend_${ENVIRONMENT}/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_set_header X-Forwarded-Host \$host;
        proxy_set_header X-Forwarded-Port \$server_port;

        proxy_cache_bypass \$http_upgrade;
        proxy_redirect off;
        proxy_buffering off;

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Error handling
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }

    # Admin static assets
    location ~* ^/admin/assets/.*\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        root $FRONTEND_ADMIN_PATH;
        try_files \$uri =404;

        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;

        # Security for admin assets
        add_header X-Frame-Options "DENY";
    }

    # Admin panel frontend
    location /admin/ {
        alias $FRONTEND_ADMIN_PATH/;
        try_files \$uri \$uri/ /admin/index.html;

        # Admin security headers
        add_header X-Frame-Options "DENY" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin" always;

        # Cache control for admin
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }

    # Upload files
    location /uploads/ {
        alias $(dirname "$BACKEND_PATH")/uploads/;

        # Security for uploads
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options "SAMEORIGIN";

        # Cache uploaded files
        expires 30d;
        add_header Cache-Control "public";

        # Prevent execution of scripts in upload directory
        location ~* \.(php|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }

    # Static assets (main frontend)
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;

        # Fallback to main frontend
        try_files \$uri =404;
    }

    # Main frontend (SPA routing)
    location / {
        try_files \$uri \$uri/ /index.html;

        # Cache control for main app
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }

    # Favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security - deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;

    # Logging
    access_log $LOG_PATH/nginx-access.log;
    error_log $LOG_PATH/nginx-error.log warn;
}
EOF

    log "Enabling Nginx site configuration..."
    ln -sf "$nginx_config" "$SITES_ENABLED/$NGINX_SITE_NAME"

    # Remove default site if it exists
    if [[ -f "$SITES_ENABLED/default" ]]; then
        log "Removing default Nginx site..."
        rm -f "$SITES_ENABLED/default"
    fi

    log "Testing Nginx configuration..."
    nginx -t

    log "Reloading Nginx..."
    systemctl reload nginx

    success "Nginx configuration created and activated successfully"
}

create_pm2_ecosystem() {
    step "Creating PM2 Ecosystem Configuration"

    local ecosystem_file="$(dirname "$BACKEND_PATH")/ecosystem.config.js"

    log "Creating PM2 ecosystem file: $ecosystem_file"

    cat > "$ecosystem_file" << EOF
// Nirvana Organics - $ENVIRONMENT Environment PM2 Configuration
// Generated on: $DEPLOYMENT_DATE

module.exports = {
  apps: [
    {
      name: '$PM2_MAIN_APP_NAME',
      script: './main.js',
      cwd: '$BACKEND_PATH',
      instances: $([[ "$ENVIRONMENT" == "production" ]] && echo "2" || echo "1"),
      exec_mode: 'cluster',
      env: {
        NODE_ENV: '$ENVIRONMENT',
        PORT: $MAIN_PORT
      },
      env_file: '$(dirname "$BACKEND_PATH")/$ENV_FILE',
      log_file: '$LOG_PATH/pm2-main-combined.log',
      out_file: '$LOG_PATH/pm2-main-out.log',
      error_file: '$LOG_PATH/pm2-main-error.log',
      pid_file: '$LOG_PATH/pm2-main.pid',
      merge_logs: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      max_memory_restart: '$([[ "$ENVIRONMENT" == "production" ]] && echo "1G" || echo "512M")',
      node_args: '--max-old-space-size=$([[ "$ENVIRONMENT" == "production" ]] && echo "1024" || echo "512")',
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      autorestart: true,
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      source_map_support: false,
      instance_var: 'INSTANCE_ID'
    },
    {
      name: '$PM2_ADMIN_APP_NAME',
      script: './admin.js',
      cwd: '$BACKEND_PATH',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: '$ENVIRONMENT',
        PORT: $ADMIN_PORT
      },
      env_file: '$(dirname "$BACKEND_PATH")/$ADMIN_ENV_FILE',
      log_file: '$LOG_PATH/pm2-admin-combined.log',
      out_file: '$LOG_PATH/pm2-admin-out.log',
      error_file: '$LOG_PATH/pm2-admin-error.log',
      pid_file: '$LOG_PATH/pm2-admin.pid',
      merge_logs: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      max_memory_restart: '512M',
      node_args: '--max-old-space-size=512',
      max_restarts: 10,
      min_uptime: '10s',
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,
      autorestart: true,
      watch: false,
      ignore_watch: ['node_modules', 'logs', 'uploads'],
      source_map_support: false
    }
  ]
};
EOF

    log "Setting PM2 ecosystem file ownership..."
    chown "$DEPLOY_USER:$DEPLOY_USER" "$ecosystem_file"
    chmod 644 "$ecosystem_file"

    success "PM2 ecosystem configuration created successfully"
}

setup_database() {
    step "Setting up Database"

    log "Testing database connection..."
    if mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -pHrishikesh@0912 -e "USE $DB_NAME; SELECT 1;" &>/dev/null; then
        success "Database connection successful"
    else
        error "Database connection failed. Please check database credentials and connectivity."
    fi

    log "Running database migrations..."
    cd "$BACKEND_PATH"
    sudo -u "$DEPLOY_USER" npm run migrate 2>/dev/null || warning "Database migration command not found or failed"

    log "Seeding database with initial data..."
    sudo -u "$DEPLOY_USER" npm run seed 2>/dev/null || warning "Database seeding command not found or failed"

    success "Database setup completed"
}

deploy_frontend() {
    step "Deploying Frontend Applications"

    # Deploy main frontend
    log "Building and deploying main frontend..."
    if [[ -d "$SCRIPT_DIR/frontend" ]]; then
        cd "$SCRIPT_DIR/frontend"

        # Copy environment file
        if [[ -f "$SCRIPT_DIR/$FRONTEND_ENV_FILE" ]]; then
            cp "$SCRIPT_DIR/$FRONTEND_ENV_FILE" .env
        fi

        # Install dependencies and build
        sudo -u "$DEPLOY_USER" npm install
        sudo -u "$DEPLOY_USER" npm run build

        # Deploy built files
        if [[ -d "dist" ]]; then
            cp -r dist/* "$FRONTEND_MAIN_PATH/"
            chown -R "$DEPLOY_USER:$DEPLOY_USER" "$FRONTEND_MAIN_PATH"
            success "Main frontend deployed successfully"
        else
            warning "Main frontend build directory not found"
        fi
    else
        warning "Main frontend source directory not found"
    fi

    # Deploy admin frontend
    log "Building and deploying admin frontend..."
    if [[ -d "$SCRIPT_DIR/admin-frontend" ]]; then
        cd "$SCRIPT_DIR/admin-frontend"

        # Copy environment file
        if [[ -f "$SCRIPT_DIR/$ADMIN_FRONTEND_ENV_FILE" ]]; then
            cp "$SCRIPT_DIR/$ADMIN_FRONTEND_ENV_FILE" .env
        fi

        # Install dependencies and build
        sudo -u "$DEPLOY_USER" npm install
        sudo -u "$DEPLOY_USER" npm run build

        # Deploy built files
        if [[ -d "dist" ]]; then
            cp -r dist/* "$FRONTEND_ADMIN_PATH/"
            chown -R "$DEPLOY_USER:$DEPLOY_USER" "$FRONTEND_ADMIN_PATH"
            success "Admin frontend deployed successfully"
        else
            warning "Admin frontend build directory not found"
        fi
    else
        warning "Admin frontend source directory not found"
    fi
}

start_services() {
    step "Starting Services"

    log "Starting PM2 applications..."
    cd "$(dirname "$BACKEND_PATH")"

    # Stop existing PM2 processes if they exist
    sudo -u "$DEPLOY_USER" pm2 delete "$PM2_MAIN_APP_NAME" 2>/dev/null || true
    sudo -u "$DEPLOY_USER" pm2 delete "$PM2_ADMIN_APP_NAME" 2>/dev/null || true

    # Start applications using ecosystem file
    sudo -u "$DEPLOY_USER" pm2 start ecosystem.config.js

    # Save PM2 configuration
    sudo -u "$DEPLOY_USER" pm2 save

    # Wait for applications to start
    sleep 10

    # Check application status
    log "Checking application status..."
    sudo -u "$DEPLOY_USER" pm2 status

    success "Services started successfully"
}

create_backup_script() {
    step "Creating Backup Script"

    local backup_script="$(dirname "$BACKEND_PATH")/backup.sh"

    cat > "$backup_script" << EOF
#!/bin/bash
# Nirvana Organics - $ENVIRONMENT Environment Backup Script
# Generated on: $DEPLOYMENT_DATE

BACKUP_DATE=\$(date '+%Y%m%d-%H%M%S')
BACKUP_DIR="$BACKUP_PATH/\$BACKUP_DATE"
LOG_FILE="$LOG_PATH/backup-\$BACKUP_DATE.log"

echo "Starting backup at \$(date)" | tee -a "\$LOG_FILE"

# Create backup directory
mkdir -p "\$BACKUP_DIR"

# Backup database
echo "Backing up database..." | tee -a "\$LOG_FILE"
mysqldump -h srv1921.hstgr.io -P 3306 -u u106832845_root -pHrishikesh@0912 $DB_NAME > "\$BACKUP_DIR/database.sql"

# Backup application files
echo "Backing up application files..." | tee -a "\$LOG_FILE"
tar -czf "\$BACKUP_DIR/backend.tar.gz" -C "$(dirname "$BACKEND_PATH")" server/
tar -czf "\$BACKUP_DIR/frontend-main.tar.gz" -C "$FRONTEND_MAIN_PATH" .
tar -czf "\$BACKUP_DIR/frontend-admin.tar.gz" -C "$FRONTEND_ADMIN_PATH" .

# Backup uploads
echo "Backing up uploads..." | tee -a "\$LOG_FILE"
tar -czf "\$BACKUP_DIR/uploads.tar.gz" -C "$(dirname "$BACKEND_PATH")" uploads/

# Backup configuration files
echo "Backing up configuration files..." | tee -a "\$LOG_FILE"
cp "$(dirname "$BACKEND_PATH")/$ENV_FILE" "\$BACKUP_DIR/"
cp "$(dirname "$BACKEND_PATH")/$ADMIN_ENV_FILE" "\$BACKUP_DIR/"
cp "$(dirname "$BACKEND_PATH")/ecosystem.config.js" "\$BACKUP_DIR/"

# Create backup info file
cat > "\$BACKUP_DIR/backup-info.txt" << EOL
Backup Date: \$BACKUP_DATE
Environment: $ENVIRONMENT
Domain: $DOMAIN
Backend Path: $BACKEND_PATH
Database: $DB_NAME
EOL

# Compress entire backup
echo "Compressing backup..." | tee -a "\$LOG_FILE"
tar -czf "$BACKUP_PATH/nirvana-$ENVIRONMENT-\$BACKUP_DATE.tar.gz" -C "$BACKUP_PATH" "\$BACKUP_DATE"
rm -rf "\$BACKUP_DIR"

# Clean old backups (keep last 7 for test, 30 for production)
RETENTION_DAYS=$([[ "$ENVIRONMENT" == "production" ]] && echo "30" || echo "7")
find "$BACKUP_PATH" -name "nirvana-$ENVIRONMENT-*.tar.gz" -mtime +\$RETENTION_DAYS -delete

echo "Backup completed at \$(date)" | tee -a "\$LOG_FILE"
EOF

    chmod +x "$backup_script"
    chown "$DEPLOY_USER:$DEPLOY_USER" "$backup_script"

    # Add to crontab
    log "Setting up backup cron job..."
    (sudo -u "$DEPLOY_USER" crontab -l 2>/dev/null; echo "0 3 * * * $backup_script") | sudo -u "$DEPLOY_USER" crontab -

    success "Backup script created and scheduled"
}

verify_deployment() {
    step "Verifying Deployment"

    log "Checking service status..."

    # Check Nginx
    if systemctl is-active --quiet nginx; then
        success "✅ Nginx is running"
    else
        error "❌ Nginx is not running"
    fi

    # Check MariaDB
    if systemctl is-active --quiet mariadb; then
        success "✅ MariaDB is running"
    else
        error "❌ MariaDB is not running"
    fi

    # Check Redis
    if systemctl is-active --quiet redis-server; then
        success "✅ Redis is running"
    else
        warning "⚠️  Redis is not running"
    fi

    # Check PM2 applications
    log "Checking PM2 applications..."
    if sudo -u "$DEPLOY_USER" pm2 describe "$PM2_MAIN_APP_NAME" &>/dev/null; then
        local main_status=$(sudo -u "$DEPLOY_USER" pm2 describe "$PM2_MAIN_APP_NAME" | grep "status" | awk '{print $4}')
        if [[ "$main_status" == "online" ]]; then
            success "✅ Main backend is running"
        else
            error "❌ Main backend is not running (status: $main_status)"
        fi
    else
        error "❌ Main backend application not found"
    fi

    if sudo -u "$DEPLOY_USER" pm2 describe "$PM2_ADMIN_APP_NAME" &>/dev/null; then
        local admin_status=$(sudo -u "$DEPLOY_USER" pm2 describe "$PM2_ADMIN_APP_NAME" | grep "status" | awk '{print $4}')
        if [[ "$admin_status" == "online" ]]; then
            success "✅ Admin backend is running"
        else
            error "❌ Admin backend is not running (status: $admin_status)"
        fi
    else
        error "❌ Admin backend application not found"
    fi

    # Check HTTP endpoints
    log "Testing HTTP endpoints..."

    # Test main site
    if curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/health" | grep -q "200"; then
        success "✅ Main site is responding"
    else
        warning "⚠️  Main site health check failed"
    fi

    # Test admin panel
    if curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/admin/" | grep -q "200"; then
        success "✅ Admin panel is accessible"
    else
        warning "⚠️  Admin panel accessibility check failed"
    fi

    # Check SSL certificate
    log "Checking SSL certificate..."
    if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" </dev/null 2>/dev/null | openssl x509 -noout -dates &>/dev/null; then
        success "✅ SSL certificate is valid"
    else
        warning "⚠️  SSL certificate check failed"
    fi

    success "Deployment verification completed"
}

print_deployment_summary() {
    step "Deployment Summary"

    echo -e "\n${GREEN}${BOLD}🎉 Nirvana Organics Deployment Completed Successfully!${NC}\n"

    echo -e "${WHITE}${BOLD}Environment Details:${NC}"
    echo -e "  Environment: ${CYAN}$ENVIRONMENT${NC}"
    echo -e "  Domain: ${CYAN}https://$DOMAIN${NC}"
    echo -e "  Deployment Date: ${CYAN}$DEPLOYMENT_DATE${NC}"
    echo -e "  Script Version: ${CYAN}$SCRIPT_VERSION${NC}"

    echo -e "\n${WHITE}${BOLD}Application URLs:${NC}"
    echo -e "  🌐 Main Site: ${CYAN}https://$DOMAIN${NC}"
    echo -e "  🔧 Admin Panel: ${CYAN}https://$DOMAIN/admin${NC}"
    echo -e "  📊 API Health: ${CYAN}https://$DOMAIN/health${NC}"

    echo -e "\n${WHITE}${BOLD}Directory Structure:${NC}"
    echo -e "  📁 Backend: ${CYAN}$BACKEND_PATH${NC}"
    echo -e "  📁 Main Frontend: ${CYAN}$FRONTEND_MAIN_PATH${NC}"
    echo -e "  📁 Admin Frontend: ${CYAN}$FRONTEND_ADMIN_PATH${NC}"
    echo -e "  📁 Uploads: ${CYAN}$(dirname "$BACKEND_PATH")/uploads${NC}"
    echo -e "  📁 Backups: ${CYAN}$BACKUP_PATH${NC}"
    echo -e "  📁 Logs: ${CYAN}$LOG_PATH${NC}"

    echo -e "\n${WHITE}${BOLD}Service Information:${NC}"
    echo -e "  🔧 Main Backend Port: ${CYAN}$MAIN_PORT${NC}"
    echo -e "  🔧 Admin Backend Port: ${CYAN}$ADMIN_PORT${NC}"
    echo -e "  📊 PM2 Main App: ${CYAN}$PM2_MAIN_APP_NAME${NC}"
    echo -e "  📊 PM2 Admin App: ${CYAN}$PM2_ADMIN_APP_NAME${NC}"

    echo -e "\n${WHITE}${BOLD}Useful Commands:${NC}"
    echo -e "  View PM2 status: ${YELLOW}sudo -u $DEPLOY_USER pm2 status${NC}"
    echo -e "  View PM2 logs: ${YELLOW}sudo -u $DEPLOY_USER pm2 logs${NC}"
    echo -e "  Restart services: ${YELLOW}sudo -u $DEPLOY_USER pm2 restart all${NC}"
    echo -e "  View Nginx logs: ${YELLOW}tail -f $LOG_PATH/nginx-*.log${NC}"
    echo -e "  Test Nginx config: ${YELLOW}nginx -t${NC}"
    echo -e "  Reload Nginx: ${YELLOW}systemctl reload nginx${NC}"
    echo -e "  Run backup: ${YELLOW}$(dirname "$BACKEND_PATH")/backup.sh${NC}"

    echo -e "\n${WHITE}${BOLD}Important Files:${NC}"
    echo -e "  📄 Main Environment: ${CYAN}$(dirname "$BACKEND_PATH")/$ENV_FILE${NC}"
    echo -e "  📄 Admin Environment: ${CYAN}$(dirname "$BACKEND_PATH")/$ADMIN_ENV_FILE${NC}"
    echo -e "  📄 PM2 Ecosystem: ${CYAN}$(dirname "$BACKEND_PATH")/ecosystem.config.js${NC}"
    echo -e "  📄 Nginx Config: ${CYAN}$SITES_AVAILABLE/$NGINX_SITE_NAME${NC}"
    echo -e "  📄 Deployment Log: ${CYAN}$LOG_FILE${NC}"

    echo -e "\n${WHITE}${BOLD}Next Steps:${NC}"
    echo -e "  1. ${CYAN}Test the application thoroughly${NC}"
    echo -e "  2. ${CYAN}Configure DNS to point to this server${NC}"
    echo -e "  3. ${CYAN}Set up monitoring and alerting${NC}"
    echo -e "  4. ${CYAN}Configure regular backups${NC}"
    echo -e "  5. ${CYAN}Review security settings${NC}"

    if [[ "$ENVIRONMENT" == "test" ]]; then
        echo -e "\n${YELLOW}${BOLD}⚠️  Test Environment Notes:${NC}"
        echo -e "  • This is a test environment with relaxed security settings"
        echo -e "  • Debug mode is enabled for troubleshooting"
        echo -e "  • Using Square sandbox for payments"
        echo -e "  • SSL certificate may take a few minutes to propagate"
    else
        echo -e "\n${RED}${BOLD}🔒 Production Environment Notes:${NC}"
        echo -e "  • This is a production environment with strict security"
        echo -e "  • Debug mode is disabled"
        echo -e "  • Using Square production for payments"
        echo -e "  • Monitor logs regularly for any issues"
        echo -e "  • Ensure regular backups are working"
    fi

    echo -e "\n${GREEN}${BOLD}Deployment completed successfully! 🚀${NC}\n"
}

# =============================================================================
# MAIN DEPLOYMENT FUNCTION
# =============================================================================

main() {
    # Print header
    echo -e "\n${WHITE}${BOLD}"
    echo "================================================================================"
    echo "                    NIRVANA ORGANICS DEPLOYMENT SCRIPT"
    echo "================================================================================"
    echo -e "${NC}"
    echo -e "Environment: ${CYAN}${BOLD}$ENVIRONMENT${NC}"
    echo -e "Domain: ${CYAN}${BOLD}$DOMAIN${NC}"
    echo -e "Version: ${CYAN}${BOLD}$SCRIPT_VERSION${NC}"
    echo -e "Date: ${CYAN}${BOLD}$DEPLOYMENT_DATE${NC}"
    echo -e "\n${WHITE}$(printf '=%.0s' {1..80})${NC}\n"

    # Pre-flight checks
    check_root
    check_os
    create_log_directory

    log "Starting Nirvana Organics deployment for $ENVIRONMENT environment"
    log "Deployment script version: $SCRIPT_VERSION"
    log "Target domain: $DOMAIN"

    # System setup
    create_deploy_user
    update_system

    # Install dependencies
    install_nodejs
    install_pm2
    install_nginx
    install_mysql
    install_redis

    # Application setup
    create_directory_structure
    deploy_backend_code
    create_environment_files
    create_nginx_configuration
    create_pm2_ecosystem
    setup_ssl_certificates

    # Database and frontend
    setup_database
    deploy_frontend

    # Start services
    start_services

    # Post-deployment
    create_backup_script
    verify_deployment
    print_deployment_summary

    log "Deployment completed successfully at $(date)"
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Trap errors and cleanup
trap 'error "Deployment failed at line $LINENO. Check the log file: $LOG_FILE"' ERR

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
