#!/bin/bash

# =============================================================================
# Quick Fix for Square SDK MODULE_NOT_FOUND Error
# =============================================================================
# 
# This script provides an immediate fix for the Square SDK dependency mismatch
# issue in the Nirvana Organics test environment.
# 
# Usage: ./quick-fix-square.sh [test|production]
# 
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-test}
if [ "$ENVIRONMENT" = "test" ]; then
    SERVER_DIR="/var/www/nirvana-test/server"
    PM2_APPS="nirvana-backend-main-test nirvana-backend-admin-test"
elif [ "$ENVIRONMENT" = "production" ]; then
    SERVER_DIR="/var/www/nirvana-production/server"
    PM2_APPS="nirvana-backend-main-production nirvana-backend-admin-production"
else
    echo -e "${RED}❌ Invalid environment. Use 'test' or 'production'${NC}"
    exit 1
fi

echo -e "${BLUE}⚡ Quick Fix for Square SDK Issue${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo "=============================================="

# Function to log with timestamp
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

log "${YELLOW}🛑 Stopping PM2 applications...${NC}"
for app in $PM2_APPS; do
    sudo -u Nirvana pm2 stop "$app" 2>/dev/null || true
done

log "${BLUE}📂 Navigating to server directory...${NC}"
cd "$SERVER_DIR"

log "${YELLOW}🔍 Checking current Square SDK installation...${NC}"
if [ -d "node_modules/square" ]; then
    log "Current Square SDK found, checking version..."
    if [ -f "node_modules/square/package.json" ]; then
        CURRENT_VERSION=$(grep '"version"' node_modules/square/package.json | cut -d'"' -f4)
        log "Current version: $CURRENT_VERSION"
    fi
else
    log "${RED}❌ Square SDK not found in node_modules${NC}"
fi

log "${YELLOW}🧹 Cleaning up problematic dependencies...${NC}"
# Remove problematic Square installations
sudo -u Nirvana rm -rf node_modules/square node_modules/@square 2>/dev/null || true

log "${YELLOW}📦 Installing correct Square SDK version...${NC}"
# Install the specific version that matches package.json
sudo -u Nirvana npm install square@39.0.0 --no-save --no-package-lock

log "${YELLOW}✅ Verifying Square SDK installation...${NC}"
if sudo -u Nirvana node -e "
try {
    const { Client, Environment } = require('square');
    console.log('✅ Square SDK loaded successfully');
    console.log('Available exports:', Object.keys(require('square')));
} catch (error) {
    console.error('❌ Square SDK test failed:', error.message);
    process.exit(1);
}
" 2>/dev/null; then
    log "${GREEN}✅ Square SDK verification successful${NC}"
else
    log "${RED}❌ Square SDK verification failed, trying alternative approach...${NC}"
    
    # Try installing from npm registry with force
    sudo -u Nirvana npm install square@39.0.0 --force --no-package-lock
    
    # Verify again
    if sudo -u Nirvana node -e "require('square'); console.log('Square SDK loaded');" 2>/dev/null; then
        log "${GREEN}✅ Square SDK verification successful (second attempt)${NC}"
    else
        log "${RED}❌ Square SDK installation failed completely${NC}"
        exit 1
    fi
fi

log "${YELLOW}🔧 Testing Square service loading...${NC}"
if sudo -u Nirvana node -e "
try {
    const SquareService = require('./services/squareService');
    console.log('✅ SquareService loaded successfully');
} catch (error) {
    console.error('❌ SquareService loading failed:', error.message);
    process.exit(1);
}
" 2>/dev/null; then
    log "${GREEN}✅ SquareService loading test passed${NC}"
else
    log "${RED}❌ SquareService loading test failed${NC}"
    exit 1
fi

log "${GREEN}🚀 Starting PM2 applications...${NC}"
for app in $PM2_APPS; do
    sudo -u Nirvana pm2 start "$app" 2>/dev/null || true
done

log "${BLUE}⏳ Waiting 5 seconds for applications to start...${NC}"
sleep 5

log "${BLUE}📊 Checking PM2 status...${NC}"
sudo -u Nirvana pm2 status

log "${BLUE}🌐 Testing application endpoints...${NC}"
if curl -s -f "http://localhost:5000/health" > /dev/null 2>&1; then
    log "${GREEN}✅ Main backend (port 5000) is responding${NC}"
else
    log "${YELLOW}⚠️  Main backend (port 5000) may still be starting...${NC}"
fi

if curl -s -f "http://localhost:3001/health" > /dev/null 2>&1; then
    log "${GREEN}✅ Admin backend (port 3001) is responding${NC}"
else
    log "${YELLOW}⚠️  Admin backend (port 3001) may still be starting...${NC}"
fi

log "${GREEN}🎉 Quick fix completed!${NC}"
log ""
log "${BLUE}📋 What was fixed:${NC}"
log "- Removed conflicting Square SDK installations"
log "- Installed correct Square SDK version (39.0.0)"
log "- Verified Square SDK and SquareService loading"
log "- Restarted PM2 applications"
log ""
log "${YELLOW}📝 Next steps:${NC}"
log "1. Monitor logs: sudo -u Nirvana pm2 logs"
log "2. Check for any remaining errors in the next few minutes"
log "3. Test Square payment functionality in the application"
log ""
log "${BLUE}🔍 If issues persist, run the comprehensive diagnostic:${NC}"
log "./fix-square-sdk-issue.sh $ENVIRONMENT"
