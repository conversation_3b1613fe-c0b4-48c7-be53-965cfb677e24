# Nirvana Organics - Quick Start Deployment Guide

## 🚀 Quick Deployment (5 Minutes)

### Prerequisites Checklist
- [ ] Fresh Ubuntu 20.04+ or Debian 11+ VPS server
- [ ] Root/sudo access to the server
- [ ] DNS configured (test.shopnirvanaorganics.com or shopnirvanaorganics.com)
- [ ] All source code files uploaded to server

### Step 1: Upload Files to Server
```bash
# Connect to your server
ssh root@your-server-ip

# Create deployment directory
mkdir -p /root/nirvana-deployment
cd /root/nirvana-deployment

# Upload your files here (via SCP, SFTP, or Git)
# Required files:
# - deploy-nirvana-organics.sh
# - server/ (backend code)
# - frontend/ (main frontend source)
# - admin-frontend/ (admin frontend source)
# - .env.* files (environment configurations)
```

### Step 2: Make Script Executable
```bash
chmod +x deploy-nirvana-organics.sh
```

### Step 3: Deploy Test Environment
```bash
# For test environment (test.shopnirvanaorganics.com)
sudo ./deploy-nirvana-organics.sh test
```

### Step 4: Deploy Production Environment
```bash
# For production environment (shopnirvanaorganics.com)
sudo ./deploy-nirvana-organics.sh production
```

### Step 5: Verify Deployment
After deployment completes, verify:
- [ ] Visit https://test.shopnirvanaorganics.com (or production domain)
- [ ] Visit https://test.shopnirvanaorganics.com/admin
- [ ] Check https://test.shopnirvanaorganics.com/health

## 🔧 Post-Deployment Commands

### Check Service Status
```bash
# Check all services
systemctl status nginx mariadb redis-server

# Check PM2 applications
sudo -u Nirvana pm2 status

# View application logs
sudo -u Nirvana pm2 logs
```

### Common Management Commands
```bash
# Restart applications
sudo -u Nirvana pm2 restart all

# Reload Nginx
systemctl reload nginx

# View logs
tail -f /var/log/nirvana-test/pm2-*.log
```

## 📁 Directory Structure After Deployment

### Test Environment
```
/var/www/nirvana-test/
├── server/              # Backend application
├── main/                # Main frontend
├── admin/               # Admin frontend
├── uploads/             # File uploads
├── .env.test           # Main backend config
├── .env.admin.test     # Admin backend config
├── ecosystem.config.js  # PM2 configuration
└── backup.sh           # Backup script
```

### Production Environment
```
/var/www/nirvana-production/
├── server/              # Backend application
├── main/                # Main frontend
├── admin/               # Admin frontend
├── uploads/             # File uploads
├── .env.production     # Main backend config
├── .env.admin.production # Admin backend config
├── ecosystem.config.js  # PM2 configuration
└── backup.sh           # Backup script
```

## 🌐 Access URLs

### Test Environment
- **Main Site**: https://test.shopnirvanaorganics.com
- **Admin Panel**: https://test.shopnirvanaorganics.com/admin
- **API Health**: https://test.shopnirvanaorganics.com/health

### Production Environment
- **Main Site**: https://shopnirvanaorganics.com
- **Admin Panel**: https://shopnirvanaorganics.com/admin
- **API Health**: https://shopnirvanaorganics.com/health

## ⚡ Troubleshooting Quick Fixes

### Issue: SSL Certificate Failed
```bash
# Manually obtain certificate
certbot --nginx -d test.shopnirvanaorganics.com
systemctl reload nginx
```

### Issue: PM2 Apps Not Running
```bash
# Check PM2 status
sudo -u Nirvana pm2 status

# Restart all apps
sudo -u Nirvana pm2 restart all

# View logs for errors
sudo -u Nirvana pm2 logs --lines 50
```

### Issue: Database Connection Failed
```bash
# Test database connection
mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -p

# Check environment file
cat /var/www/nirvana-test/.env.test | grep DB_
```

### Issue: Frontend Not Loading
```bash
# Check frontend files exist
ls -la /var/www/nirvana-test/main/
ls -la /var/www/nirvana-test/admin/

# Check Nginx configuration
nginx -t
systemctl reload nginx
```

## 📊 Monitoring Commands

### Real-time Monitoring
```bash
# System resources
htop

# PM2 monitoring dashboard
sudo -u Nirvana pm2 monit

# Live log monitoring
sudo -u Nirvana pm2 logs --lines 0 --raw | grep ERROR
```

### Health Checks
```bash
# Test main site
curl -I https://test.shopnirvanaorganics.com/health

# Test admin panel
curl -I https://test.shopnirvanaorganics.com/admin/

# Check SSL certificate
openssl s_client -connect test.shopnirvanaorganics.com:443 -servername test.shopnirvanaorganics.com
```

## 🔄 Backup and Recovery

### Manual Backup
```bash
# Run backup script
sudo -u Nirvana /var/www/nirvana-test/backup.sh

# List backups
ls -la /var/backups/nirvana-organics-test/
```

### Quick Recovery
```bash
# Stop applications
sudo -u Nirvana pm2 stop all

# Restore from backup (replace YYYYMMDD-HHMMSS with actual backup date)
cd /var/backups/nirvana-organics-test/
tar -xzf nirvana-test-YYYYMMDD-HHMMSS.tar.gz

# Restore database
mysql -h srv1921.hstgr.io -u u106832845_root -p u106832845_nirvana < YYYYMMDD-HHMMSS/database.sql

# Restart applications
sudo -u Nirvana pm2 start all
```

## 🔐 Security Checklist

### Immediate Security Steps
- [ ] Change default SSH port
- [ ] Disable root SSH login
- [ ] Enable UFW firewall
- [ ] Set up fail2ban
- [ ] Configure automatic security updates

```bash
# Quick security setup
ufw enable
ufw allow 22/tcp
ufw allow 80/tcp
ufw allow 443/tcp

# Install fail2ban
apt install fail2ban
systemctl enable fail2ban
```

## 📞 Getting Help

### Log Locations
- **Deployment Logs**: `/var/log/nirvana-test/deployment-*.log`
- **Application Logs**: `/var/log/nirvana-test/pm2-*.log`
- **Nginx Logs**: `/var/log/nirvana-test/nginx-*.log`
- **System Logs**: `journalctl -f`

### Support Resources
- **Full Documentation**: See `DEPLOYMENT_GUIDE.md`
- **Troubleshooting**: Check logs and common issues section
- **Community**: Stack Overflow, GitHub Issues

## 🎯 Success Indicators

Your deployment is successful when:
- ✅ All services show "active (running)" status
- ✅ PM2 applications show "online" status  
- ✅ Websites load without errors
- ✅ SSL certificates are valid
- ✅ Database queries execute successfully
- ✅ Admin panel is accessible
- ✅ Health endpoints return 200 OK

## 📝 Next Steps After Deployment

1. **Test thoroughly** - Verify all functionality works
2. **Configure monitoring** - Set up alerts and health checks
3. **Security hardening** - Follow security best practices
4. **Performance tuning** - Optimize based on usage patterns
5. **Backup verification** - Test backup and recovery procedures

---

**Need more detailed information?** See the complete `DEPLOYMENT_GUIDE.md` for comprehensive instructions, troubleshooting, and maintenance procedures.

**Deployment Time**: 15-30 minutes  
**Script Version**: 2.0.0  
**Last Updated**: 2025-01-01
