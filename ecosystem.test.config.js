// =============================================================================
// PM2 Ecosystem Configuration - Test Environment
// =============================================================================
// 
// Environment: Test
// Domain: test.shopnirvanaorganics.com
// Working Directory: /var/www/nirvana-test/server
// User: Nirvana
// 
// Installation:
//   1. Copy this file to /var/www/nirvana-test/ecosystem.config.js
//   2. Start applications: pm2 start ecosystem.config.js
//   3. Save PM2 configuration: pm2 save
//   4. Setup startup script: pm2 startup
// 
// Management Commands:
//   pm2 start ecosystem.config.js
//   pm2 restart ecosystem.config.js
//   pm2 stop ecosystem.config.js
//   pm2 reload ecosystem.config.js
//   pm2 delete ecosystem.config.js
// 
// =============================================================================

module.exports = {
  apps: [
    {
      // Main Backend Application
      name: 'nirvana-backend-main-test',
      script: './main.js',
      cwd: '/var/www/nirvana-test/server',
      
      // Process Configuration
      instances: 1, // Single instance for test environment
      exec_mode: 'fork', // Fork mode for test (simpler debugging)
      
      // Environment Configuration
      env: {
        NODE_ENV: 'test',
        PORT: 5000,
        ENV_FILE: '/var/www/nirvana-test/.env.test'
      },
      
      // Logging Configuration
      log_file: '/var/log/nirvana-test/pm2-main-combined.log',
      out_file: '/var/log/nirvana-test/pm2-main-out.log',
      error_file: '/var/log/nirvana-test/pm2-main-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process Management
      autorestart: true,
      watch: false, // Disable watch in production-like environments
      max_memory_restart: '500M', // Restart if memory usage exceeds 500MB
      restart_delay: 4000, // Wait 4 seconds before restart
      max_restarts: 10, // Maximum restarts within min_uptime
      min_uptime: '10s', // Minimum uptime before considering restart successful
      
      // Health Monitoring
      health_check_grace_period: 3000, // Grace period for health checks
      health_check_fatal_exceptions: true,
      
      // User and Permissions
      uid: 'Nirvana',
      gid: 'Nirvana',
      
      // Advanced Options
      node_args: [
        '--max-old-space-size=512', // Limit memory usage
        '--optimize-for-size' // Optimize for memory usage
      ],
      
      // Error Handling
      kill_timeout: 5000, // Time to wait before force killing
      listen_timeout: 3000, // Time to wait for app to listen
      
      // Source Map Support (for better error traces)
      source_map_support: true,
      
      // Time Configuration
      time: true,
      
      // Instance Variables
      instance_var: 'INSTANCE_ID',
      
      // Interpreter Options
      interpreter: 'node',
      interpreter_args: '--harmony',
      
      // Process Title
      name: 'nirvana-main-test'
    },
    
    {
      // Admin Backend Application
      name: 'nirvana-backend-admin-test',
      script: './admin-server.js',
      cwd: '/var/www/nirvana-test/server',
      
      // Process Configuration
      instances: 1, // Single instance for test environment
      exec_mode: 'fork', // Fork mode for test
      
      // Environment Configuration
      env: {
        NODE_ENV: 'test',
        PORT: 3001,
        ENV_FILE: '/var/www/nirvana-test/.env.admin.test'
      },
      
      // Logging Configuration
      log_file: '/var/log/nirvana-test/pm2-admin-combined.log',
      out_file: '/var/log/nirvana-test/pm2-admin-out.log',
      error_file: '/var/log/nirvana-test/pm2-admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process Management
      autorestart: true,
      watch: false,
      max_memory_restart: '300M', // Lower memory limit for admin server
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Health Monitoring
      health_check_grace_period: 3000,
      health_check_fatal_exceptions: true,
      
      // User and Permissions
      uid: 'Nirvana',
      gid: 'Nirvana',
      
      // Advanced Options
      node_args: [
        '--max-old-space-size=256', // Lower memory limit for admin
        '--optimize-for-size'
      ],
      
      // Error Handling
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Source Map Support
      source_map_support: true,
      
      // Time Configuration
      time: true,
      
      // Instance Variables
      instance_var: 'INSTANCE_ID',
      
      // Interpreter Options
      interpreter: 'node',
      interpreter_args: '--harmony',
      
      // Process Title
      name: 'nirvana-admin-test'
    }
  ],
  
  // Deployment Configuration (optional)
  deploy: {
    test: {
      user: 'Nirvana',
      host: 'localhost',
      ref: 'origin/test',
      repo: '**************:username/nirvana-organics.git', // Update with actual repo
      path: '/var/www/nirvana-test',
      'pre-deploy-local': '',
      'post-deploy': 'npm install --production && pm2 reload ecosystem.config.js --env test',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  },
  
  // Global PM2 Configuration
  pmx: {
    enabled: true,
    network: true, // Network monitoring
    ports: true,   // Port monitoring
    
    // Custom Metrics
    custom_probes: [
      {
        name: 'CPU usage',
        probe: function() {
          return process.cpuUsage();
        }
      },
      {
        name: 'Memory usage',
        probe: function() {
          return process.memoryUsage();
        }
      }
    ],
    
    // Actions
    actions: [
      {
        action_name: 'clear_logs',
        action_function: function(reply) {
          const fs = require('fs');
          const logFiles = [
            '/var/log/nirvana-test/pm2-main-combined.log',
            '/var/log/nirvana-test/pm2-main-out.log',
            '/var/log/nirvana-test/pm2-main-error.log',
            '/var/log/nirvana-test/pm2-admin-combined.log',
            '/var/log/nirvana-test/pm2-admin-out.log',
            '/var/log/nirvana-test/pm2-admin-error.log'
          ];
          
          logFiles.forEach(file => {
            try {
              fs.writeFileSync(file, '');
            } catch (err) {
              console.error(`Failed to clear log file ${file}:`, err.message);
            }
          });
          
          reply({ success: true, message: 'Log files cleared' });
        }
      },
      {
        action_name: 'get_system_info',
        action_function: function(reply) {
          const os = require('os');
          reply({
            hostname: os.hostname(),
            platform: os.platform(),
            arch: os.arch(),
            cpus: os.cpus().length,
            memory: {
              total: Math.round(os.totalmem() / 1024 / 1024) + ' MB',
              free: Math.round(os.freemem() / 1024 / 1024) + ' MB'
            },
            uptime: Math.round(os.uptime() / 60) + ' minutes'
          });
        }
      }
    ]
  },
  
  // Log Rotation Configuration
  log_rotation: {
    enabled: true,
    max_size: '10M',
    retain: 30,
    compress: true,
    date_format: 'YYYY-MM-DD_HH-mm-ss',
    rotateModule: true,
    rotateInterval: '0 0 * * *' // Daily at midnight
  },
  
  // Module Configuration
  module_conf: {
    // PM2 Log Rotate Module
    'pm2-logrotate': {
      max_size: '10M',
      retain: 30,
      compress: true,
      dateFormat: 'YYYY-MM-DD_HH-mm-ss',
      workerInterval: 30,
      rotateInterval: '0 0 * * *',
      rotateModule: true
    },
    
    // PM2 Auto Pull Module (for automatic deployments)
    'pm2-auto-pull': {
      apps: [
        {
          name: 'nirvana-backend-main-test',
          script: '/var/www/nirvana-test/server/main.js',
          watch: [
            '/var/www/nirvana-test/server'
          ],
          ignore_watch: [
            'node_modules',
            'logs',
            '*.log'
          ],
          interval: 300000 // Check every 5 minutes
        }
      ]
    }
  }
};

// Export configuration for different environments
if (process.env.NODE_ENV === 'development') {
  // Development overrides
  module.exports.apps.forEach(app => {
    app.watch = true;
    app.ignore_watch = ['node_modules', 'logs', '*.log'];
    app.watch_options = {
      followSymlinks: false,
      usePolling: false
    };
  });
}

// Validation function
function validateConfig() {
  const config = module.exports;
  
  // Validate apps configuration
  if (!config.apps || !Array.isArray(config.apps)) {
    throw new Error('Apps configuration is required and must be an array');
  }
  
  config.apps.forEach((app, index) => {
    if (!app.name) {
      throw new Error(`App at index ${index} must have a name`);
    }
    if (!app.script) {
      throw new Error(`App ${app.name} must have a script`);
    }
    if (!app.cwd) {
      throw new Error(`App ${app.name} must have a working directory (cwd)`);
    }
  });
  
  console.log('✅ PM2 ecosystem configuration is valid');
  return true;
}

// Run validation if this file is executed directly
if (require.main === module) {
  try {
    validateConfig();
    console.log('PM2 Test Environment Configuration:');
    console.log('- Main Backend: nirvana-backend-main-test (Port 5000)');
    console.log('- Admin Backend: nirvana-backend-admin-test (Port 3001)');
    console.log('- Working Directory: /var/www/nirvana-test/server');
    console.log('- Log Directory: /var/log/nirvana-test/');
    console.log('- User: Nirvana');
    console.log('- Environment: Test');
  } catch (error) {
    console.error('❌ Configuration validation failed:', error.message);
    process.exit(1);
  }
}
