// =============================================================================
// PM2 Ecosystem Configuration - Production Environment
// =============================================================================
// 
// Environment: Production
// Domain: shopnirvanaorganics.com
// Working Directory: /var/www/nirvana-production/server
// User: Nirvana
// 
// Installation:
//   1. Copy this file to /var/www/nirvana-production/ecosystem.config.js
//   2. Start applications: pm2 start ecosystem.config.js --env production
//   3. Save PM2 configuration: pm2 save
//   4. Setup startup script: pm2 startup
// 
// Management Commands:
//   pm2 start ecosystem.config.js --env production
//   pm2 restart ecosystem.config.js --env production
//   pm2 stop ecosystem.config.js
//   pm2 reload ecosystem.config.js --env production
//   pm2 delete ecosystem.config.js
// 
// =============================================================================

const os = require('os');
const numCPUs = os.cpus().length;

module.exports = {
  apps: [
    {
      // Main Backend Application - Production Cluster
      name: 'nirvana-backend-main-production',
      script: './main.js',
      cwd: '/var/www/nirvana-production/server',
      
      // Process Configuration - Cluster Mode for Production
      instances: Math.max(2, Math.min(numCPUs, 4)), // 2-4 instances based on CPU cores
      exec_mode: 'cluster', // Cluster mode for load balancing
      
      // Environment Configuration
      env: {
        NODE_ENV: 'production',
        PORT: 5000,
        ENV_FILE: '/var/www/nirvana-production/.env.production'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 5000,
        ENV_FILE: '/var/www/nirvana-production/.env.production',
        NODE_OPTIONS: '--max-old-space-size=1024'
      },
      
      // Logging Configuration
      log_file: '/var/log/nirvana-production/pm2-main-combined.log',
      out_file: '/var/log/nirvana-production/pm2-main-out.log',
      error_file: '/var/log/nirvana-production/pm2-main-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process Management - Production Settings
      autorestart: true,
      watch: false, // Never enable watch in production
      max_memory_restart: '1G', // Higher memory limit for production
      restart_delay: 2000, // Faster restart for production
      max_restarts: 15, // More restart attempts
      min_uptime: '30s', // Longer minimum uptime
      
      // Health Monitoring
      health_check_grace_period: 5000,
      health_check_fatal_exceptions: true,
      
      // User and Permissions
      uid: 'Nirvana',
      gid: 'Nirvana',
      
      // Advanced Options - Production Optimized
      node_args: [
        '--max-old-space-size=1024', // Higher memory limit
        '--optimize-for-size',
        '--gc-interval=100',
        '--harmony'
      ],
      
      // Error Handling
      kill_timeout: 10000, // Longer kill timeout for graceful shutdown
      listen_timeout: 5000,
      
      // Source Map Support
      source_map_support: false, // Disable in production for performance
      
      // Time Configuration
      time: true,
      
      // Instance Variables
      instance_var: 'INSTANCE_ID',
      
      // Interpreter Options
      interpreter: 'node',
      interpreter_args: '--harmony --optimize-for-size',
      
      // Process Title
      name: 'nirvana-main-prod',
      
      // Cluster Configuration
      increment_var: 'PORT',
      
      // Performance Monitoring
      pmx: true,
      
      // Graceful Shutdown
      kill_retry_time: 100,
      
      // Load Balancing
      instance_var: 'INSTANCE_ID',
      combine_logs: true
    },
    
    {
      // Admin Backend Application - Single Instance (Security)
      name: 'nirvana-backend-admin-production',
      script: './admin-server.js',
      cwd: '/var/www/nirvana-production/server',
      
      // Process Configuration - Single instance for admin security
      instances: 1, // Single instance for admin (security consideration)
      exec_mode: 'fork', // Fork mode for admin
      
      // Environment Configuration
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        ENV_FILE: '/var/www/nirvana-production/.env.admin.production'
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001,
        ENV_FILE: '/var/www/nirvana-production/.env.admin.production',
        NODE_OPTIONS: '--max-old-space-size=512'
      },
      
      // Logging Configuration
      log_file: '/var/log/nirvana-production/pm2-admin-combined.log',
      out_file: '/var/log/nirvana-production/pm2-admin-out.log',
      error_file: '/var/log/nirvana-production/pm2-admin-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process Management
      autorestart: true,
      watch: false,
      max_memory_restart: '512M', // Moderate memory limit for admin
      restart_delay: 3000,
      max_restarts: 10,
      min_uptime: '30s',
      
      // Health Monitoring
      health_check_grace_period: 5000,
      health_check_fatal_exceptions: true,
      
      // User and Permissions
      uid: 'Nirvana',
      gid: 'Nirvana',
      
      // Advanced Options
      node_args: [
        '--max-old-space-size=512',
        '--optimize-for-size',
        '--harmony'
      ],
      
      // Error Handling
      kill_timeout: 10000,
      listen_timeout: 5000,
      
      // Source Map Support
      source_map_support: false,
      
      // Time Configuration
      time: true,
      
      // Instance Variables
      instance_var: 'INSTANCE_ID',
      
      // Interpreter Options
      interpreter: 'node',
      interpreter_args: '--harmony --optimize-for-size',
      
      // Process Title
      name: 'nirvana-admin-prod'
    }
  ],
  
  // Deployment Configuration
  deploy: {
    production: {
      user: 'Nirvana',
      host: 'localhost',
      ref: 'origin/main',
      repo: '**************:username/nirvana-organics.git', // Update with actual repo
      path: '/var/www/nirvana-production',
      'pre-deploy-local': '',
      'post-deploy': 'npm install --production && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no',
      env: {
        NODE_ENV: 'production'
      }
    }
  },
  
  // Global PM2 Configuration - Production Optimized
  pmx: {
    enabled: true,
    network: true,
    ports: true,
    
    // Custom Metrics for Production Monitoring
    custom_probes: [
      {
        name: 'CPU usage',
        probe: function() {
          return process.cpuUsage();
        }
      },
      {
        name: 'Memory usage',
        probe: function() {
          const usage = process.memoryUsage();
          return {
            rss: Math.round(usage.rss / 1024 / 1024) + ' MB',
            heapTotal: Math.round(usage.heapTotal / 1024 / 1024) + ' MB',
            heapUsed: Math.round(usage.heapUsed / 1024 / 1024) + ' MB',
            external: Math.round(usage.external / 1024 / 1024) + ' MB'
          };
        }
      },
      {
        name: 'Event Loop Latency',
        probe: function() {
          const start = process.hrtime();
          setImmediate(() => {
            const delta = process.hrtime(start);
            return Math.round((delta[0] * 1000) + (delta[1] / 1000000));
          });
        }
      },
      {
        name: 'Active Handles',
        probe: function() {
          return process._getActiveHandles().length;
        }
      }
    ],
    
    // Production Actions
    actions: [
      {
        action_name: 'clear_logs',
        action_function: function(reply) {
          const fs = require('fs');
          const logFiles = [
            '/var/log/nirvana-production/pm2-main-combined.log',
            '/var/log/nirvana-production/pm2-main-out.log',
            '/var/log/nirvana-production/pm2-main-error.log',
            '/var/log/nirvana-production/pm2-admin-combined.log',
            '/var/log/nirvana-production/pm2-admin-out.log',
            '/var/log/nirvana-production/pm2-admin-error.log'
          ];
          
          logFiles.forEach(file => {
            try {
              fs.writeFileSync(file, '');
            } catch (err) {
              console.error(`Failed to clear log file ${file}:`, err.message);
            }
          });
          
          reply({ success: true, message: 'Production log files cleared' });
        }
      },
      {
        action_name: 'get_system_info',
        action_function: function(reply) {
          const os = require('os');
          reply({
            hostname: os.hostname(),
            platform: os.platform(),
            arch: os.arch(),
            cpus: os.cpus().length,
            memory: {
              total: Math.round(os.totalmem() / 1024 / 1024) + ' MB',
              free: Math.round(os.freemem() / 1024 / 1024) + ' MB',
              used: Math.round((os.totalmem() - os.freemem()) / 1024 / 1024) + ' MB'
            },
            uptime: Math.round(os.uptime() / 60) + ' minutes',
            loadavg: os.loadavg()
          });
        }
      },
      {
        action_name: 'force_gc',
        action_function: function(reply) {
          if (global.gc) {
            global.gc();
            reply({ success: true, message: 'Garbage collection forced' });
          } else {
            reply({ success: false, message: 'Garbage collection not available' });
          }
        }
      },
      {
        action_name: 'get_process_info',
        action_function: function(reply) {
          reply({
            pid: process.pid,
            version: process.version,
            platform: process.platform,
            arch: process.arch,
            uptime: Math.round(process.uptime()) + ' seconds',
            cwd: process.cwd(),
            memory: process.memoryUsage(),
            cpu: process.cpuUsage()
          });
        }
      }
    ]
  },
  
  // Log Rotation Configuration - Production
  log_rotation: {
    enabled: true,
    max_size: '50M', // Larger log files for production
    retain: 60, // Keep more logs in production
    compress: true,
    date_format: 'YYYY-MM-DD_HH-mm-ss',
    rotateModule: true,
    rotateInterval: '0 0 * * *' // Daily at midnight
  },
  
  // Module Configuration - Production
  module_conf: {
    // PM2 Log Rotate Module
    'pm2-logrotate': {
      max_size: '50M',
      retain: 60,
      compress: true,
      dateFormat: 'YYYY-MM-DD_HH-mm-ss',
      workerInterval: 30,
      rotateInterval: '0 0 * * *',
      rotateModule: true
    },
    
    // PM2 Server Monitor (if available)
    'pm2-server-monit': {
      enabled: true,
      port: 8080,
      refresh: 5000
    }
  }
};

// Production-specific optimizations
if (process.env.NODE_ENV === 'production') {
  // Enable cluster mode optimizations
  module.exports.apps.forEach(app => {
    if (app.exec_mode === 'cluster') {
      // Cluster-specific optimizations
      app.kill_timeout = 15000; // Longer kill timeout for graceful shutdown
      app.wait_ready = true; // Wait for ready signal
      app.listen_timeout = 10000; // Longer listen timeout
      
      // Memory and CPU optimizations
      app.node_args.push('--max-semi-space-size=64');
      app.node_args.push('--max-executable-size=128');
    }
  });
}

// Validation function
function validateConfig() {
  const config = module.exports;
  
  // Validate apps configuration
  if (!config.apps || !Array.isArray(config.apps)) {
    throw new Error('Apps configuration is required and must be an array');
  }
  
  config.apps.forEach((app, index) => {
    if (!app.name) {
      throw new Error(`App at index ${index} must have a name`);
    }
    if (!app.script) {
      throw new Error(`App ${app.name} must have a script`);
    }
    if (!app.cwd) {
      throw new Error(`App ${app.name} must have a working directory (cwd)`);
    }
    
    // Production-specific validations
    if (app.watch === true) {
      throw new Error(`App ${app.name} has watch enabled - this is not allowed in production`);
    }
  });
  
  console.log('✅ PM2 production ecosystem configuration is valid');
  return true;
}

// Run validation if this file is executed directly
if (require.main === module) {
  try {
    validateConfig();
    console.log('PM2 Production Environment Configuration:');
    console.log(`- Main Backend: nirvana-backend-main-production (Port 5000, ${Math.max(2, Math.min(numCPUs, 4))} instances)`);
    console.log('- Admin Backend: nirvana-backend-admin-production (Port 3001, 1 instance)');
    console.log('- Working Directory: /var/www/nirvana-production/server');
    console.log('- Log Directory: /var/log/nirvana-production/');
    console.log('- User: Nirvana');
    console.log('- Environment: Production');
    console.log(`- CPU Cores: ${numCPUs}`);
  } catch (error) {
    console.error('❌ Configuration validation failed:', error.message);
    process.exit(1);
  }
}
