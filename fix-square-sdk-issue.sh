#!/bin/bash

# =============================================================================
# Square SDK Issue Diagnostic and Fix Script
# =============================================================================
# 
# This script diagnoses and fixes MODULE_NOT_FOUND errors related to the
# Square SDK in the Nirvana Organics test environment deployment.
# 
# Usage: ./fix-square-sdk-issue.sh [test|production]
# 
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-test}
if [ "$ENVIRONMENT" = "test" ]; then
    SERVER_DIR="/var/www/nirvana-test/server"
    LOG_DIR="/var/log/nirvana-test"
    PM2_APPS="nirvana-backend-main-test nirvana-backend-admin-test"
elif [ "$ENVIRONMENT" = "production" ]; then
    SERVER_DIR="/var/www/nirvana-production/server"
    LOG_DIR="/var/log/nirvana-production"
    PM2_APPS="nirvana-backend-main-production nirvana-backend-admin-production"
else
    echo -e "${RED}❌ Invalid environment. Use 'test' or 'production'${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 Square SDK Diagnostic and Fix Script${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo -e "${BLUE}Server Directory: ${SERVER_DIR}${NC}"
echo "=============================================="

# Function to log with timestamp
log() {
    echo -e "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check if running as correct user
check_user() {
    if [ "$EUID" -eq 0 ]; then
        echo -e "${YELLOW}⚠️  Running as root. Will switch to Nirvana user for npm operations.${NC}"
        SUDO_CMD="sudo -u Nirvana"
    else
        SUDO_CMD=""
    fi
}

# Function to stop PM2 applications
stop_applications() {
    log "${YELLOW}🛑 Stopping PM2 applications...${NC}"
    
    for app in $PM2_APPS; do
        if sudo -u Nirvana pm2 list | grep -q "$app"; then
            log "Stopping $app..."
            sudo -u Nirvana pm2 stop "$app" || true
        else
            log "Application $app not found in PM2"
        fi
    done
}

# Function to start PM2 applications
start_applications() {
    log "${GREEN}🚀 Starting PM2 applications...${NC}"
    
    # Start applications using ecosystem config
    if [ -f "${SERVER_DIR}/../ecosystem.config.js" ]; then
        log "Starting applications using ecosystem config..."
        sudo -u Nirvana pm2 start "${SERVER_DIR}/../ecosystem.config.js" --env "$ENVIRONMENT" || true
    else
        log "Ecosystem config not found, starting individual applications..."
        for app in $PM2_APPS; do
            sudo -u Nirvana pm2 start "$app" || true
        done
    fi
    
    # Wait for applications to start
    sleep 5
    
    # Show PM2 status
    sudo -u Nirvana pm2 status
}

# Function to diagnose Square SDK issues
diagnose_square_sdk() {
    log "${BLUE}🔍 Diagnosing Square SDK installation...${NC}"
    
    # Check if server directory exists
    if [ ! -d "$SERVER_DIR" ]; then
        log "${RED}❌ Server directory not found: $SERVER_DIR${NC}"
        return 1
    fi
    
    cd "$SERVER_DIR"
    
    # Check package.json
    log "📋 Checking package.json..."
    if [ -f "package.json" ]; then
        if grep -q "square" package.json; then
            log "${GREEN}✅ Square dependency found in package.json${NC}"
            grep "square" package.json
        else
            log "${RED}❌ Square dependency not found in package.json${NC}"
        fi
    else
        log "${RED}❌ package.json not found in server directory${NC}"
        return 1
    fi
    
    # Check package-lock.json
    log "🔒 Checking package-lock.json..."
    if [ -f "package-lock.json" ]; then
        if grep -q "square" package-lock.json; then
            log "${GREEN}✅ Square dependency found in package-lock.json${NC}"
        else
            log "${RED}❌ Square dependency not found in package-lock.json${NC}"
        fi
    else
        log "${YELLOW}⚠️  package-lock.json not found${NC}"
    fi
    
    # Check node_modules
    log "📦 Checking node_modules..."
    if [ -d "node_modules" ]; then
        if [ -d "node_modules/square" ]; then
            log "${GREEN}✅ Square module found in node_modules${NC}"
            ls -la "node_modules/square/" | head -10
        else
            log "${RED}❌ Square module not found in node_modules${NC}"
        fi
        
        # Check for @square/square (alternative package name)
        if [ -d "node_modules/@square" ]; then
            log "${GREEN}✅ @square namespace found in node_modules${NC}"
            ls -la "node_modules/@square/"
        else
            log "${YELLOW}⚠️  @square namespace not found in node_modules${NC}"
        fi
    else
        log "${RED}❌ node_modules directory not found${NC}"
        return 1
    fi
    
    # Check Square SDK version and structure
    if [ -d "node_modules/square" ]; then
        log "📋 Checking Square SDK structure..."
        if [ -f "node_modules/square/package.json" ]; then
            log "Square SDK version:"
            grep '"version"' "node_modules/square/package.json" || true
        fi
        
        # Check for main entry points
        if [ -f "node_modules/square/dist/cjs/index.js" ]; then
            log "${GREEN}✅ Square CJS entry point found${NC}"
        else
            log "${RED}❌ Square CJS entry point missing${NC}"
        fi
        
        if [ -f "node_modules/square/dist/esm/index.js" ]; then
            log "${GREEN}✅ Square ESM entry point found${NC}"
        else
            log "${YELLOW}⚠️  Square ESM entry point missing${NC}"
        fi
    fi
    
    # Test Node.js module resolution
    log "🧪 Testing Node.js module resolution..."
    $SUDO_CMD node -e "
        try {
            const square = require('square');
            console.log('✅ Square SDK loaded successfully');
            console.log('Square SDK version:', square.version || 'Unknown');
        } catch (error) {
            console.error('❌ Failed to load Square SDK:', error.message);
            console.error('Error code:', error.code);
            console.error('Module path:', error.path || 'Unknown');
        }
    " || true
}

# Function to fix Square SDK installation
fix_square_sdk() {
    log "${YELLOW}🔧 Fixing Square SDK installation...${NC}"
    
    cd "$SERVER_DIR"
    
    # Backup current node_modules if it exists
    if [ -d "node_modules" ]; then
        log "📦 Backing up current node_modules..."
        $SUDO_CMD mv node_modules node_modules.backup.$(date +%Y%m%d_%H%M%S) || true
    fi
    
    # Remove package-lock.json to force fresh resolution
    if [ -f "package-lock.json" ]; then
        log "🔒 Removing package-lock.json for fresh dependency resolution..."
        $SUDO_CMD rm package-lock.json
    fi
    
    # Clear npm cache
    log "🧹 Clearing npm cache..."
    $SUDO_CMD npm cache clean --force || true
    
    # Install dependencies with specific Square SDK version
    log "📦 Installing dependencies with correct Square SDK..."
    
    # First, install the correct Square SDK version
    log "Installing Square SDK v39.0.0..."
    $SUDO_CMD npm install square@39.0.0 --save --no-package-lock || {
        log "${RED}❌ Failed to install Square SDK v39.0.0, trying latest version...${NC}"
        $SUDO_CMD npm install square --save --no-package-lock || {
            log "${RED}❌ Failed to install Square SDK, trying alternative package...${NC}"
            $SUDO_CMD npm install @square/square --save --no-package-lock || {
                log "${RED}❌ All Square SDK installation attempts failed${NC}"
                return 1
            }
        }
    }
    
    # Install all other dependencies
    log "📦 Installing all dependencies..."
    $SUDO_CMD npm install --production || {
        log "${RED}❌ Failed to install dependencies${NC}"
        return 1
    }
    
    # Verify installation
    log "✅ Verifying Square SDK installation..."
    if $SUDO_CMD node -e "require('square'); console.log('Square SDK loaded successfully');" 2>/dev/null; then
        log "${GREEN}✅ Square SDK installation successful${NC}"
    else
        log "${RED}❌ Square SDK installation verification failed${NC}"
        return 1
    fi
}

# Function to check PM2 logs for errors
check_pm2_logs() {
    log "${BLUE}📋 Checking PM2 logs for errors...${NC}"
    
    for app in $PM2_APPS; do
        log "Checking logs for $app..."
        
        # Check if log files exist
        if [ -f "${LOG_DIR}/pm2-${app##*-}-error.log" ]; then
            log "Recent errors in ${app}:"
            tail -20 "${LOG_DIR}/pm2-${app##*-}-error.log" | grep -i "error\|module\|square" || log "No recent errors found"
        else
            log "Error log file not found for $app"
        fi
        
        echo "---"
    done
}

# Function to test Square service functionality
test_square_service() {
    log "${BLUE}🧪 Testing Square service functionality...${NC}"
    
    cd "$SERVER_DIR"
    
    # Test Square service loading
    $SUDO_CMD node -e "
        try {
            const SquareService = require('./services/squareService');
            console.log('✅ SquareService loaded successfully');
            
            if (SquareService.isSquareAvailable()) {
                console.log('✅ Square SDK is available and configured');
            } else {
                console.log('⚠️  Square SDK loaded but not configured (missing credentials)');
            }
        } catch (error) {
            console.error('❌ Failed to load SquareService:', error.message);
            console.error('Stack trace:', error.stack);
        }
    " || true
}

# Function to update Square service for better error handling
update_square_service() {
    log "${YELLOW}🔧 Updating Square service for better error handling...${NC}"
    
    cd "$SERVER_DIR"
    
    # Backup original squareService.js
    if [ -f "services/squareService.js" ]; then
        $SUDO_CMD cp services/squareService.js services/squareService.js.backup.$(date +%Y%m%d_%H%M%S)
        
        # Add better error handling to squareService.js
        $SUDO_CMD node -e "
            const fs = require('fs');
            let content = fs.readFileSync('services/squareService.js', 'utf8');
            
            // Add more robust error handling at the top
            const errorHandlingCode = \`
// Enhanced error handling for Square SDK loading
let squareLoadError = null;
\`;
            
            if (!content.includes('squareLoadError')) {
                content = errorHandlingCode + content;
                fs.writeFileSync('services/squareService.js', content);
                console.log('✅ Enhanced error handling added to squareService.js');
            } else {
                console.log('✅ Error handling already present in squareService.js');
            }
        " || log "Failed to update squareService.js"
    fi
}

# Main execution
main() {
    check_user
    
    log "${BLUE}🚀 Starting Square SDK diagnostic and fix process...${NC}"
    
    # Stop applications first
    stop_applications
    
    # Diagnose the issue
    diagnose_square_sdk
    
    # Check current PM2 logs
    check_pm2_logs
    
    # Ask user if they want to proceed with fix
    echo ""
    read -p "Do you want to proceed with fixing the Square SDK installation? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Fix Square SDK installation
        fix_square_sdk
        
        # Update Square service
        update_square_service
        
        # Test Square service
        test_square_service
        
        # Start applications
        start_applications
        
        # Wait and check logs again
        log "${BLUE}⏳ Waiting 10 seconds for applications to stabilize...${NC}"
        sleep 10
        
        # Final verification
        log "${BLUE}🔍 Final verification...${NC}"
        check_pm2_logs
        
        # Test endpoints
        log "${BLUE}🌐 Testing application endpoints...${NC}"
        if curl -s "http://localhost:5000/health" > /dev/null; then
            log "${GREEN}✅ Main backend (port 5000) is responding${NC}"
        else
            log "${RED}❌ Main backend (port 5000) is not responding${NC}"
        fi
        
        if curl -s "http://localhost:3001/health" > /dev/null; then
            log "${GREEN}✅ Admin backend (port 3001) is responding${NC}"
        else
            log "${RED}❌ Admin backend (port 3001) is not responding${NC}"
        fi
        
        log "${GREEN}🎉 Square SDK fix process completed!${NC}"
        log "${BLUE}📋 Summary:${NC}"
        log "- Environment: $ENVIRONMENT"
        log "- Server Directory: $SERVER_DIR"
        log "- PM2 Applications: $PM2_APPS"
        log ""
        log "${YELLOW}📝 Next Steps:${NC}"
        log "1. Monitor PM2 logs: sudo -u Nirvana pm2 logs"
        log "2. Check application status: sudo -u Nirvana pm2 status"
        log "3. Test Square functionality in the application"
        log "4. Verify payment processing works correctly"
        
    else
        log "${YELLOW}⏭️  Fix process cancelled by user${NC}"
        
        # Start applications back up
        start_applications
    fi
}

# Run main function
main "$@"
