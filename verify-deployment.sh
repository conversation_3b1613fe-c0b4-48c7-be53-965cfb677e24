#!/bin/bash

# =============================================================================
# Nirvana Organics - Deployment Verification Script
# =============================================================================
# 
# Comprehensive verification script for Nirvana Organics deployment
# Tests all components and provides detailed status report
# 
# Usage:
#   ./verify-deployment.sh [test|production]
# 
# Examples:
#   ./verify-deployment.sh test
#   ./verify-deployment.sh production
# 
# =============================================================================

set -e

# =============================================================================
# CONFIGURATION
# =============================================================================

ENVIRONMENT="${1:-test}"
SCRIPT_VERSION="1.0.0"
VERIFICATION_DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Validate environment
if [[ "$ENVIRONMENT" != "test" && "$ENVIRONMENT" != "production" ]]; then
    echo "❌ Error: Invalid environment '$ENVIRONMENT'"
    echo "Valid environments: test, production"
    exit 1
fi

# Environment-specific configuration
if [[ "$ENVIRONMENT" == "test" ]]; then
    DOMAIN="test.shopnirvanaorganics.com"
    BACKEND_PATH="/var/www/nirvana-test/server"
    FRONTEND_MAIN_PATH="/var/www/nirvana-test/main"
    FRONTEND_ADMIN_PATH="/var/www/nirvana-test/admin"
    PM2_MAIN_APP="nirvana-backend-main-test"
    PM2_ADMIN_APP="nirvana-backend-admin-test"
    LOG_PATH="/var/log/nirvana-test"
    MAIN_PORT="5000"
    ADMIN_PORT="3001"
else
    DOMAIN="shopnirvanaorganics.com"
    BACKEND_PATH="/var/www/nirvana-production/server"
    FRONTEND_MAIN_PATH="/var/www/nirvana-production/main"
    FRONTEND_ADMIN_PATH="/var/www/nirvana-production/admin"
    PM2_MAIN_APP="nirvana-backend-main-production"
    PM2_ADMIN_APP="nirvana-backend-admin-production"
    LOG_PATH="/var/log/nirvana-production"
    MAIN_PORT="5000"
    ADMIN_PORT="3001"
fi

DEPLOY_USER="Nirvana"

# =============================================================================
# COLORS AND FORMATTING
# =============================================================================

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
BOLD='\033[1m'
NC='\033[0m'

# Test result counters
TESTS_PASSED=0
TESTS_FAILED=0
TESTS_WARNING=0
TOTAL_TESTS=0

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

print_header() {
    echo -e "\n${WHITE}${BOLD}"
    echo "================================================================================"
    echo "                    NIRVANA ORGANICS DEPLOYMENT VERIFICATION"
    echo "================================================================================"
    echo -e "${NC}"
    echo -e "Environment: ${CYAN}${BOLD}$ENVIRONMENT${NC}"
    echo -e "Domain: ${CYAN}${BOLD}$DOMAIN${NC}"
    echo -e "Verification Date: ${CYAN}${BOLD}$VERIFICATION_DATE${NC}"
    echo -e "Script Version: ${CYAN}${BOLD}$SCRIPT_VERSION${NC}"
    echo -e "\n${WHITE}$(printf '=%.0s' {1..80})${NC}\n"
}

test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    case "$result" in
        "PASS")
            echo -e "${GREEN}✅ PASS${NC} - $test_name: $message"
            TESTS_PASSED=$((TESTS_PASSED + 1))
            ;;
        "FAIL")
            echo -e "${RED}❌ FAIL${NC} - $test_name: $message"
            TESTS_FAILED=$((TESTS_FAILED + 1))
            ;;
        "WARN")
            echo -e "${YELLOW}⚠️  WARN${NC} - $test_name: $message"
            TESTS_WARNING=$((TESTS_WARNING + 1))
            ;;
    esac
}

section_header() {
    local section_name="$1"
    echo -e "\n${BLUE}${BOLD}🔍 Testing: $section_name${NC}"
    echo -e "${BLUE}$(printf '─%.0s' {1..60})${NC}"
}

# =============================================================================
# TEST FUNCTIONS
# =============================================================================

test_system_services() {
    section_header "System Services"
    
    # Test Nginx
    if systemctl is-active --quiet nginx; then
        test_result "Nginx Service" "PASS" "Running and active"
    else
        test_result "Nginx Service" "FAIL" "Not running or inactive"
    fi
    
    # Test MariaDB
    if systemctl is-active --quiet mariadb; then
        test_result "MariaDB Service" "PASS" "Running and active"
    else
        test_result "MariaDB Service" "FAIL" "Not running or inactive"
    fi
    
    # Test Redis
    if systemctl is-active --quiet redis-server; then
        test_result "Redis Service" "PASS" "Running and active"
    else
        test_result "Redis Service" "WARN" "Not running (optional service)"
    fi
}

test_pm2_applications() {
    section_header "PM2 Applications"
    
    # Test main backend
    if sudo -u "$DEPLOY_USER" pm2 describe "$PM2_MAIN_APP" &>/dev/null; then
        local main_status=$(sudo -u "$DEPLOY_USER" pm2 describe "$PM2_MAIN_APP" | grep "status" | awk '{print $4}' | head -1)
        if [[ "$main_status" == "online" ]]; then
            test_result "Main Backend" "PASS" "Online and running"
        else
            test_result "Main Backend" "FAIL" "Status: $main_status"
        fi
    else
        test_result "Main Backend" "FAIL" "Application not found"
    fi
    
    # Test admin backend
    if sudo -u "$DEPLOY_USER" pm2 describe "$PM2_ADMIN_APP" &>/dev/null; then
        local admin_status=$(sudo -u "$DEPLOY_USER" pm2 describe "$PM2_ADMIN_APP" | grep "status" | awk '{print $4}' | head -1)
        if [[ "$admin_status" == "online" ]]; then
            test_result "Admin Backend" "PASS" "Online and running"
        else
            test_result "Admin Backend" "FAIL" "Status: $admin_status"
        fi
    else
        test_result "Admin Backend" "FAIL" "Application not found"
    fi
}

test_network_connectivity() {
    section_header "Network Connectivity"
    
    # Test local ports
    if netstat -tlnp | grep ":$MAIN_PORT " &>/dev/null; then
        test_result "Main Port ($MAIN_PORT)" "PASS" "Port is listening"
    else
        test_result "Main Port ($MAIN_PORT)" "FAIL" "Port not listening"
    fi
    
    if netstat -tlnp | grep ":$ADMIN_PORT " &>/dev/null; then
        test_result "Admin Port ($ADMIN_PORT)" "PASS" "Port is listening"
    else
        test_result "Admin Port ($ADMIN_PORT)" "FAIL" "Port not listening"
    fi
    
    # Test HTTP ports
    if netstat -tlnp | grep ":80 " &>/dev/null; then
        test_result "HTTP Port (80)" "PASS" "Port is listening"
    else
        test_result "HTTP Port (80)" "FAIL" "Port not listening"
    fi
    
    if netstat -tlnp | grep ":443 " &>/dev/null; then
        test_result "HTTPS Port (443)" "PASS" "Port is listening"
    else
        test_result "HTTPS Port (443)" "FAIL" "Port not listening"
    fi
}

test_database_connectivity() {
    section_header "Database Connectivity"
    
    # Test database connection
    if mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -pHrishikesh@0912 -e "SELECT 1;" &>/dev/null; then
        test_result "Database Connection" "PASS" "Successfully connected to database"
    else
        test_result "Database Connection" "FAIL" "Cannot connect to database"
    fi
    
    # Test database schema
    if mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -pHrishikesh@0912 -e "USE u106832845_nirvana; SHOW TABLES;" &>/dev/null; then
        local table_count=$(mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -pHrishikesh@0912 -e "USE u106832845_nirvana; SHOW TABLES;" 2>/dev/null | wc -l)
        if [[ $table_count -gt 1 ]]; then
            test_result "Database Schema" "PASS" "Database contains $((table_count-1)) tables"
        else
            test_result "Database Schema" "WARN" "Database appears empty"
        fi
    else
        test_result "Database Schema" "FAIL" "Cannot access database schema"
    fi
}

test_web_endpoints() {
    section_header "Web Endpoints"
    
    # Test main site
    local main_response=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/" 2>/dev/null || echo "000")
    if [[ "$main_response" == "200" ]]; then
        test_result "Main Site" "PASS" "Returns HTTP 200"
    else
        test_result "Main Site" "FAIL" "Returns HTTP $main_response"
    fi
    
    # Test admin panel
    local admin_response=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/admin/" 2>/dev/null || echo "000")
    if [[ "$admin_response" == "200" ]]; then
        test_result "Admin Panel" "PASS" "Returns HTTP 200"
    else
        test_result "Admin Panel" "FAIL" "Returns HTTP $admin_response"
    fi
    
    # Test health endpoint
    local health_response=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/health" 2>/dev/null || echo "000")
    if [[ "$health_response" == "200" ]]; then
        test_result "Health Endpoint" "PASS" "Returns HTTP 200"
    else
        test_result "Health Endpoint" "FAIL" "Returns HTTP $health_response"
    fi
    
    # Test API endpoint
    local api_response=$(curl -s -o /dev/null -w "%{http_code}" "https://$DOMAIN/api/health" 2>/dev/null || echo "000")
    if [[ "$api_response" == "200" ]]; then
        test_result "API Health" "PASS" "Returns HTTP 200"
    else
        test_result "API Health" "WARN" "Returns HTTP $api_response (may be expected)"
    fi
}

test_ssl_certificates() {
    section_header "SSL Certificates"
    
    # Test SSL certificate validity
    if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" </dev/null 2>/dev/null | openssl x509 -noout -dates &>/dev/null; then
        local cert_info=$(openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" </dev/null 2>/dev/null | openssl x509 -noout -dates)
        local not_after=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
        test_result "SSL Certificate" "PASS" "Valid certificate (expires: $not_after)"
    else
        test_result "SSL Certificate" "FAIL" "Invalid or missing certificate"
    fi
    
    # Test HTTPS redirect
    local redirect_response=$(curl -s -o /dev/null -w "%{http_code}" "http://$DOMAIN/" 2>/dev/null || echo "000")
    if [[ "$redirect_response" == "301" || "$redirect_response" == "302" ]]; then
        test_result "HTTPS Redirect" "PASS" "HTTP redirects to HTTPS"
    else
        test_result "HTTPS Redirect" "WARN" "HTTP does not redirect (returns $redirect_response)"
    fi
}

test_file_structure() {
    section_header "File Structure"
    
    # Test backend files
    if [[ -d "$BACKEND_PATH" && -f "$BACKEND_PATH/main.js" ]]; then
        test_result "Backend Files" "PASS" "Backend directory and main.js exist"
    else
        test_result "Backend Files" "FAIL" "Backend files missing"
    fi
    
    # Test main frontend files
    if [[ -d "$FRONTEND_MAIN_PATH" && -f "$FRONTEND_MAIN_PATH/index.html" ]]; then
        test_result "Main Frontend" "PASS" "Frontend directory and index.html exist"
    else
        test_result "Main Frontend" "FAIL" "Main frontend files missing"
    fi
    
    # Test admin frontend files
    if [[ -d "$FRONTEND_ADMIN_PATH" && -f "$FRONTEND_ADMIN_PATH/index.html" ]]; then
        test_result "Admin Frontend" "PASS" "Admin directory and index.html exist"
    else
        test_result "Admin Frontend" "FAIL" "Admin frontend files missing"
    fi
    
    # Test configuration files
    local env_file="$(dirname "$BACKEND_PATH")/.env.$ENVIRONMENT"
    if [[ -f "$env_file" ]]; then
        test_result "Environment Config" "PASS" "Environment file exists"
    else
        test_result "Environment Config" "FAIL" "Environment file missing"
    fi
    
    # Test uploads directory
    local uploads_dir="$(dirname "$BACKEND_PATH")/uploads"
    if [[ -d "$uploads_dir" ]]; then
        test_result "Uploads Directory" "PASS" "Uploads directory exists"
    else
        test_result "Uploads Directory" "WARN" "Uploads directory missing"
    fi
}

test_log_files() {
    section_header "Log Files"
    
    # Test log directory
    if [[ -d "$LOG_PATH" ]]; then
        test_result "Log Directory" "PASS" "Log directory exists"
    else
        test_result "Log Directory" "FAIL" "Log directory missing"
    fi
    
    # Test PM2 logs
    if [[ -f "$LOG_PATH/pm2-main-combined.log" ]]; then
        test_result "PM2 Main Logs" "PASS" "PM2 main log file exists"
    else
        test_result "PM2 Main Logs" "WARN" "PM2 main log file missing"
    fi
    
    if [[ -f "$LOG_PATH/pm2-admin-combined.log" ]]; then
        test_result "PM2 Admin Logs" "PASS" "PM2 admin log file exists"
    else
        test_result "PM2 Admin Logs" "WARN" "PM2 admin log file missing"
    fi
    
    # Test Nginx logs
    if [[ -f "$LOG_PATH/nginx-access.log" ]]; then
        test_result "Nginx Access Logs" "PASS" "Nginx access log exists"
    else
        test_result "Nginx Access Logs" "WARN" "Nginx access log missing"
    fi
}

test_backup_system() {
    section_header "Backup System"
    
    # Test backup script
    local backup_script="$(dirname "$BACKEND_PATH")/backup.sh"
    if [[ -f "$backup_script" && -x "$backup_script" ]]; then
        test_result "Backup Script" "PASS" "Backup script exists and is executable"
    else
        test_result "Backup Script" "FAIL" "Backup script missing or not executable"
    fi
    
    # Test backup directory
    local backup_dir="/var/backups/nirvana-organics-$ENVIRONMENT"
    if [[ -d "$backup_dir" ]]; then
        test_result "Backup Directory" "PASS" "Backup directory exists"
    else
        test_result "Backup Directory" "WARN" "Backup directory missing"
    fi
    
    # Test backup cron job
    if sudo -u "$DEPLOY_USER" crontab -l 2>/dev/null | grep -q "backup.sh"; then
        test_result "Backup Cron Job" "PASS" "Backup cron job configured"
    else
        test_result "Backup Cron Job" "WARN" "Backup cron job not found"
    fi
}

print_summary() {
    echo -e "\n${WHITE}${BOLD}"
    echo "================================================================================"
    echo "                              VERIFICATION SUMMARY"
    echo "================================================================================"
    echo -e "${NC}"
    
    echo -e "${WHITE}${BOLD}Test Results:${NC}"
    echo -e "  ${GREEN}✅ Passed: $TESTS_PASSED${NC}"
    echo -e "  ${RED}❌ Failed: $TESTS_FAILED${NC}"
    echo -e "  ${YELLOW}⚠️  Warnings: $TESTS_WARNING${NC}"
    echo -e "  📊 Total Tests: $TOTAL_TESTS"
    
    local success_rate=$((TESTS_PASSED * 100 / TOTAL_TESTS))
    echo -e "\n${WHITE}${BOLD}Success Rate: ${CYAN}$success_rate%${NC}"
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        echo -e "\n${GREEN}${BOLD}🎉 Deployment Verification PASSED!${NC}"
        echo -e "${GREEN}Your Nirvana Organics deployment is working correctly.${NC}"
    else
        echo -e "\n${RED}${BOLD}❌ Deployment Verification FAILED!${NC}"
        echo -e "${RED}Please review the failed tests and fix the issues.${NC}"
    fi
    
    if [[ $TESTS_WARNING -gt 0 ]]; then
        echo -e "\n${YELLOW}${BOLD}⚠️  Warning: Some tests returned warnings.${NC}"
        echo -e "${YELLOW}These may not be critical but should be reviewed.${NC}"
    fi
    
    echo -e "\n${WHITE}${BOLD}Environment Information:${NC}"
    echo -e "  Environment: ${CYAN}$ENVIRONMENT${NC}"
    echo -e "  Domain: ${CYAN}https://$DOMAIN${NC}"
    echo -e "  Verification Date: ${CYAN}$VERIFICATION_DATE${NC}"
    
    echo -e "\n${WHITE}${BOLD}Quick Access URLs:${NC}"
    echo -e "  🌐 Main Site: ${CYAN}https://$DOMAIN${NC}"
    echo -e "  🔧 Admin Panel: ${CYAN}https://$DOMAIN/admin${NC}"
    echo -e "  📊 Health Check: ${CYAN}https://$DOMAIN/health${NC}"
    
    echo -e "\n${WHITE}$(printf '=%.0s' {1..80})${NC}\n"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    print_header
    
    test_system_services
    test_pm2_applications
    test_network_connectivity
    test_database_connectivity
    test_web_endpoints
    test_ssl_certificates
    test_file_structure
    test_log_files
    test_backup_system
    
    print_summary
    
    # Exit with appropriate code
    if [[ $TESTS_FAILED -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
