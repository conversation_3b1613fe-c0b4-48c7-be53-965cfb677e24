# Nirvana Organics - Comprehensive Deployment Guide

## Table of Contents
1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [System Requirements](#system-requirements)
4. [Pre-Deployment Preparation](#pre-deployment-preparation)
5. [Deployment Process](#deployment-process)
6. [Post-Deployment Verification](#post-deployment-verification)
7. [Environment Configuration](#environment-configuration)
8. [Troubleshooting](#troubleshooting)
9. [Maintenance](#maintenance)
10. [Security Considerations](#security-considerations)

## Overview

This guide provides comprehensive instructions for deploying the Nirvana Organics e-commerce platform on a fresh Hostinger VPS server. The deployment script supports both test and production environments with a unified configuration approach.

### Architecture Overview
- **Dual Backend Architecture**: Main customer-facing server (port 5000) and admin server (port 3001)
- **Frontend Applications**: Separate builds for main customer interface and admin panel
- **Database**: MySQL/MariaDB with Sequelize ORM
- **Web Server**: Nginx with SSL/TLS termination and reverse proxy
- **Process Management**: PM2 for Node.js application management
- **Caching**: Redis for session and data caching

### Environment Structure
```
Test Environment (test.shopnirvanaorganics.com):
├── /var/www/nirvana-test/
│   ├── server/          # Backend application
│   ├── main/            # Main frontend
│   ├── admin/           # Admin frontend
│   └── uploads/         # File uploads

Production Environment (shopnirvanaorganics.com):
├── /var/www/nirvana-production/
│   ├── server/          # Backend application
│   ├── main/            # Main frontend
│   ├── admin/           # Admin frontend
│   └── uploads/         # File uploads
```

## Prerequisites

### Server Requirements
- **Operating System**: Ubuntu 20.04 LTS or Debian 11+ (recommended)
- **RAM**: Minimum 2GB (4GB+ recommended for production)
- **Storage**: Minimum 20GB SSD (50GB+ recommended for production)
- **CPU**: 2+ cores recommended
- **Network**: Public IP address with ports 80, 443, 22 accessible

### Domain Configuration
Before deployment, ensure:
1. **Test Environment**: DNS A record for `test.shopnirvanaorganics.com` pointing to server IP
2. **Production Environment**: DNS A record for `shopnirvanaorganics.com` pointing to server IP
3. DNS propagation completed (can take up to 48 hours)

### Access Requirements
- Root or sudo access to the server
- SSH access to the server
- Database credentials for Hostinger MySQL

### Third-Party Services
- **Square Payment Processing**: Sandbox and production accounts configured
- **Google OAuth**: Client ID and secret for authentication
- **Email Service**: SMTP credentials for transactional emails

## System Requirements

### Software Dependencies (Automatically Installed)
- Node.js 18.x LTS
- npm (latest)
- PM2 process manager
- Nginx web server
- MariaDB/MySQL client
- Redis server
- Certbot for SSL certificates
- Git version control

### Network Requirements
- Outbound HTTPS (443) for package downloads and API calls
- Inbound HTTP (80) and HTTPS (443) for web traffic
- Outbound MySQL (3306) for database connectivity
- Outbound SMTP (587) for email delivery

## Pre-Deployment Preparation

### 1. Server Setup
```bash
# Connect to your server
ssh ssh root@*************

# Update system (if not done recently)
apt update && apt upgrade -y

# Install basic tools
apt install -y curl wget git unzip
```

### 2. Upload Deployment Files
Upload the following files to your server:
- `deploy-nirvana-organics.sh` (main deployment script)
- `server/` directory (backend application code)
- `frontend/` directory (main frontend source)
- `admin-frontend/` directory (admin frontend source)
- Environment configuration files (`.env.*`)

### 3. Verify File Structure
Ensure your deployment directory contains:
```
/root/nirvana-deployment/
├── deploy-nirvana-organics.sh
├── server/
├── frontend/
├── admin-frontend/
├── .env.test
├── .env.admin.test
├── .env.frontend.test
├── .env.admin.frontend.test
├── .env.production
├── .env.admin.production
├── .env.frontend.production
└── .env.admin.frontend.production
```

### 4. Make Script Executable
```bash
chmod +x deploy-nirvana-organics.sh
```

## Deployment Process

### Test Environment Deployment
```bash
# Navigate to deployment directory
cd /path/to/deployment/files

# Run deployment for test environment
sudo ./deploy-nirvana-organics.sh test
```

### Production Environment Deployment
```bash
# Navigate to deployment directory
cd /path/to/deployment/files

# Run deployment for production environment
sudo ./deploy-nirvana-organics.sh production
```

### Deployment Steps Overview
The script automatically performs the following steps:

1. **System Validation**
   - Checks root privileges
   - Validates operating system
   - Creates log directories

2. **User Management**
   - Creates deployment user (`Nirvana`)
   - Sets up SSH and PM2 directories
   - Configures sudo permissions

3. **System Updates**
   - Updates package repositories
   - Upgrades system packages
   - Installs essential tools

4. **Software Installation**
   - Node.js 18.x LTS
   - PM2 process manager
   - Nginx web server
   - MariaDB client tools
   - Redis cache server
   - SSL certificate tools

5. **Directory Structure**
   - Creates application directories
   - Sets up upload directories
   - Configures backup locations
   - Sets proper permissions

6. **Application Deployment**
   - Copies backend code
   - Installs Node.js dependencies
   - Creates environment files
   - Builds and deploys frontends

7. **Service Configuration**
   - Nginx virtual host setup
   - PM2 ecosystem configuration
   - SSL certificate generation
   - Database connection testing

8. **Service Startup**
   - Starts PM2 applications
   - Enables service auto-start
   - Configures backup scripts
   - Validates deployment

### Deployment Duration
- **Test Environment**: 15-25 minutes
- **Production Environment**: 20-30 minutes

*Note: Duration may vary based on server performance and network speed.*

## Post-Deployment Verification

### Automatic Verification
The deployment script automatically verifies:
- ✅ Service status (Nginx, MariaDB, Redis, PM2)
- ✅ Application health endpoints
- ✅ SSL certificate validity
- ✅ Frontend accessibility

### Manual Verification Steps

#### 1. Check Service Status
```bash
# Check all services
systemctl status nginx mariadb redis-server

# Check PM2 applications
sudo -u Nirvana pm2 status

# View PM2 logs
sudo -u Nirvana pm2 logs
```

#### 2. Test Web Access
- **Main Site**: https://test.shopnirvanaorganics.com (or production domain)
- **Admin Panel**: https://test.shopnirvanaorganics.com/admin
- **Health Check**: https://test.shopnirvanaorganics.com/health

#### 3. Verify Database Connectivity
```bash
# Test database connection
mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -p -e "USE u106832845_nirvana; SHOW TABLES;"
```

#### 4. Check Log Files
```bash
# Application logs
tail -f /var/log/nirvana-test/pm2-*.log

# Nginx logs
tail -f /var/log/nirvana-test/nginx-*.log

# System logs
journalctl -u nginx -f
```

### Expected Results
- All services should show "active (running)" status
- PM2 applications should show "online" status
- Websites should load without errors
- SSL certificates should be valid and trusted
- Database queries should execute successfully

## Environment Configuration

### Environment Variables
The deployment creates environment-specific configuration files:

#### Test Environment
- `.env.test` - Main backend configuration
- `.env.admin.test` - Admin backend configuration
- `.env.frontend.test` - Main frontend build configuration
- `.env.admin.frontend.test` - Admin frontend build configuration

#### Production Environment
- `.env.production` - Main backend configuration
- `.env.admin.production` - Admin backend configuration
- `.env.frontend.production` - Main frontend build configuration
- `.env.admin.frontend.production` - Admin frontend build configuration

### Key Configuration Differences

| Setting | Test Environment | Production Environment |
|---------|------------------|------------------------|
| Domain | test.shopnirvanaorganics.com | shopnirvanaorganics.com |
| Debug Mode | Enabled | Disabled |
| SQL Logging | Enabled | Disabled |
| Rate Limiting | Relaxed (1000/15min) | Strict (500/15min) |
| Square Environment | Sandbox | Production |
| SSL Mode | Required | Required |
| Session Timeout | 24 hours | 30 minutes (admin) |
| Log Level | Debug | Info |
| Backup Retention | 7 days | 30 days |

### Customizing Configuration
To modify environment settings:

1. Edit the appropriate `.env` file:
   ```bash
   nano /var/www/nirvana-test/.env.test
   ```

2. Restart the affected services:
   ```bash
   sudo -u Nirvana pm2 restart nirvana-backend-main-test
   ```

3. Verify changes:
   ```bash
   sudo -u Nirvana pm2 logs nirvana-backend-main-test
   ```

## Troubleshooting

### Common Issues and Solutions

#### 1. Deployment Script Fails

**Issue**: Script exits with error during execution
```bash
❌ [ERROR] Deployment failed at line X
```

**Solutions**:
```bash
# Check the deployment log
tail -f /var/log/nirvana-test/deployment-*.log

# Verify system requirements
df -h  # Check disk space
free -h  # Check memory
systemctl status nginx mariadb redis-server

# Re-run with verbose logging
bash -x ./deploy-nirvana-organics.sh test
```

#### 2. SSL Certificate Issues

**Issue**: SSL certificate generation fails
```bash
⚠️ SSL certificate setup failed - continuing with HTTP configuration
```

**Solutions**:
```bash
# Verify DNS resolution
nslookup test.shopnirvanaorganics.com

# Check domain accessibility
curl -I http://test.shopnirvanaorganics.com

# Manually obtain certificate
certbot --nginx -d test.shopnirvanaorganics.com

# Check certificate status
certbot certificates
```

#### 3. Database Connection Failures

**Issue**: Cannot connect to database
```bash
❌ Database connection failed
```

**Solutions**:
```bash
# Test database connectivity
mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -p

# Check firewall rules
ufw status

# Verify database credentials in environment file
cat /var/www/nirvana-test/.env.test | grep DB_

# Test from application server
telnet srv1921.hstgr.io 3306
```

#### 4. PM2 Application Issues

**Issue**: PM2 applications not starting
```bash
❌ Main backend is not running
```

**Solutions**:
```bash
# Check PM2 status
sudo -u Nirvana pm2 status

# View detailed logs
sudo -u Nirvana pm2 logs --lines 50

# Restart applications
sudo -u Nirvana pm2 restart all

# Check ecosystem configuration
cat /var/www/nirvana-test/ecosystem.config.js

# Manual application start
cd /var/www/nirvana-test/server
sudo -u Nirvana node main.js
```

#### 5. Nginx Configuration Issues

**Issue**: Nginx fails to start or serve content
```bash
❌ Nginx is not running
```

**Solutions**:
```bash
# Test Nginx configuration
nginx -t

# Check Nginx error logs
tail -f /var/log/nginx/error.log

# Verify site configuration
cat /etc/nginx/sites-available/nirvana-organics-test

# Restart Nginx
systemctl restart nginx

# Check port conflicts
netstat -tlnp | grep :80
netstat -tlnp | grep :443
```

#### 6. Frontend Not Loading

**Issue**: Frontend shows 404 or blank page

**Solutions**:
```bash
# Check frontend files
ls -la /var/www/nirvana-test/main/
ls -la /var/www/nirvana-test/admin/

# Verify Nginx configuration
nginx -t && systemctl reload nginx

# Check file permissions
find /var/www/nirvana-test -type f -exec ls -l {} \;

# Rebuild frontend
cd /path/to/frontend/source
npm run build
cp -r dist/* /var/www/nirvana-test/main/
```

#### 7. High Memory Usage

**Issue**: Server running out of memory

**Solutions**:
```bash
# Check memory usage
free -h
ps aux --sort=-%mem | head -10

# Restart PM2 applications
sudo -u Nirvana pm2 restart all

# Adjust PM2 memory limits
sudo -u Nirvana pm2 start ecosystem.config.js --update-env

# Add swap space (if needed)
fallocate -l 2G /swapfile
chmod 600 /swapfile
mkswap /swapfile
swapon /swapfile
```

### Log File Locations

#### Application Logs
- **PM2 Main**: `/var/log/nirvana-test/pm2-main-*.log`
- **PM2 Admin**: `/var/log/nirvana-test/pm2-admin-*.log`
- **Application**: `/var/log/nirvana-test/main.log`
- **Admin Application**: `/var/log/nirvana-test/admin.log`

#### System Logs
- **Nginx Access**: `/var/log/nirvana-test/nginx-access.log`
- **Nginx Error**: `/var/log/nirvana-test/nginx-error.log`
- **Deployment**: `/var/log/nirvana-test/deployment-*.log`
- **Backup**: `/var/log/nirvana-test/backup-*.log`

#### System Service Logs
```bash
# Nginx logs
journalctl -u nginx -f

# MariaDB logs
journalctl -u mariadb -f

# Redis logs
journalctl -u redis-server -f

# System logs
journalctl -f
```

### Performance Monitoring

#### Real-time Monitoring Commands
```bash
# System resources
htop
iotop
nethogs

# PM2 monitoring
sudo -u Nirvana pm2 monit

# Nginx status
curl http://localhost/nginx_status

# Database performance
mysql -h srv1921.hstgr.io -u u106832845_root -p -e "SHOW PROCESSLIST;"
```

## Maintenance

### Regular Maintenance Tasks

#### Daily Tasks
- Monitor application logs for errors
- Check disk space usage
- Verify backup completion
- Monitor SSL certificate expiration

#### Weekly Tasks
- Review security logs
- Update system packages
- Check database performance
- Verify backup integrity

#### Monthly Tasks
- Rotate log files
- Update Node.js dependencies
- Review and update SSL certificates
- Performance optimization review

### Backup and Recovery

#### Automated Backups
The deployment script creates an automated backup system:

```bash
# Backup script location
/var/www/nirvana-test/backup.sh

# Manual backup execution
sudo -u Nirvana /var/www/nirvana-test/backup.sh

# View backup files
ls -la /var/backups/nirvana-organics-test/
```

#### Backup Contents
- Database dump (SQL format)
- Application code (tar.gz)
- Frontend builds (tar.gz)
- Upload files (tar.gz)
- Configuration files
- Environment variables

#### Recovery Process
```bash
# Stop applications
sudo -u Nirvana pm2 stop all

# Extract backup
cd /var/backups/nirvana-organics-test/
tar -xzf nirvana-test-YYYYMMDD-HHMMSS.tar.gz

# Restore database
mysql -h srv1921.hstgr.io -u u106832845_root -p u106832845_nirvana < database.sql

# Restore application files
tar -xzf backend.tar.gz -C /var/www/nirvana-test/
tar -xzf frontend-main.tar.gz -C /var/www/nirvana-test/main/
tar -xzf frontend-admin.tar.gz -C /var/www/nirvana-test/admin/
tar -xzf uploads.tar.gz -C /var/www/nirvana-test/

# Restart applications
sudo -u Nirvana pm2 start all
```

### Updates and Upgrades

#### Application Updates
```bash
# Stop applications
sudo -u Nirvana pm2 stop all

# Backup current version
/var/www/nirvana-test/backup.sh

# Update application code
cd /var/www/nirvana-test/server
git pull origin main  # or copy new files
npm install --production

# Update frontend
cd /path/to/frontend/source
npm run build
cp -r dist/* /var/www/nirvana-test/main/

# Restart applications
sudo -u Nirvana pm2 start all
```

#### System Updates
```bash
# Update system packages
apt update && apt upgrade -y

# Update Node.js (if needed)
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Update PM2
npm install -g pm2@latest
sudo -u Nirvana pm2 update

# Restart services
systemctl restart nginx
sudo -u Nirvana pm2 restart all
```

## Security Considerations

### Server Security

#### Firewall Configuration
```bash
# Enable UFW firewall
ufw enable

# Allow essential ports
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS

# Check firewall status
ufw status verbose
```

#### SSH Security
```bash
# Edit SSH configuration
nano /etc/ssh/sshd_config

# Recommended settings:
# Port 2222                    # Change default port
# PermitRootLogin no          # Disable root login
# PasswordAuthentication no   # Use key-based auth only
# MaxAuthTries 3              # Limit login attempts

# Restart SSH service
systemctl restart sshd
```

#### System Hardening
```bash
# Disable unused services
systemctl disable bluetooth
systemctl disable cups

# Set up automatic security updates
apt install unattended-upgrades
dpkg-reconfigure unattended-upgrades

# Configure fail2ban
apt install fail2ban
systemctl enable fail2ban
```

### Application Security

#### Environment Variables
- Store sensitive data in environment files
- Use strong, unique passwords and secrets
- Rotate API keys and tokens regularly
- Never commit secrets to version control

#### Database Security
```bash
# Use strong database passwords
# Enable SSL connections
# Limit database user permissions
# Regular security updates

# Example secure database user creation
mysql -h srv1921.hstgr.io -u root -p
CREATE USER 'nirvana_app'@'%' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON u106832845_nirvana.* TO 'nirvana_app'@'%';
FLUSH PRIVILEGES;
```

#### SSL/TLS Configuration
- Use strong cipher suites
- Enable HSTS headers
- Regular certificate renewal
- Monitor certificate expiration

#### Rate Limiting
The deployment includes rate limiting:
- **General requests**: 50/second
- **API requests**: 20/second
- **Admin requests**: 10/second

### Monitoring and Alerting

#### Log Monitoring
```bash
# Set up log rotation
nano /etc/logrotate.d/nirvana-organics

# Content:
/var/log/nirvana-*/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 Nirvana Nirvana
    postrotate
        systemctl reload nginx
    endscript
}
```

#### Health Monitoring
```bash
# Create health check script
cat > /usr/local/bin/health-check.sh << 'EOF'
#!/bin/bash
# Health check for Nirvana Organics

DOMAIN="test.shopnirvanaorganics.com"  # Change for production
EMAIL="<EMAIL>"

# Check main site
if ! curl -sf "https://$DOMAIN/health" > /dev/null; then
    echo "Main site health check failed" | mail -s "Alert: Main site down" $EMAIL
fi

# Check admin panel
if ! curl -sf "https://$DOMAIN/admin/" > /dev/null; then
    echo "Admin panel health check failed" | mail -s "Alert: Admin panel down" $EMAIL
fi

# Check PM2 processes
if ! sudo -u Nirvana pm2 describe nirvana-backend-main-test > /dev/null; then
    echo "Main backend process not running" | mail -s "Alert: Backend down" $EMAIL
fi
EOF

chmod +x /usr/local/bin/health-check.sh

# Add to crontab
(crontab -l; echo "*/5 * * * * /usr/local/bin/health-check.sh") | crontab -
```

### Compliance and Best Practices

#### Data Protection
- Implement proper data encryption
- Regular security audits
- User data anonymization
- GDPR compliance measures

#### Access Control
- Role-based access control (RBAC)
- Multi-factor authentication (MFA)
- Regular access reviews
- Principle of least privilege

#### Incident Response
- Document incident response procedures
- Regular security training
- Backup and recovery testing
- Security incident logging

## Quick Reference

### Essential Commands

#### Service Management
```bash
# Check all services
systemctl status nginx mariadb redis-server

# PM2 operations
sudo -u Nirvana pm2 status
sudo -u Nirvana pm2 restart all
sudo -u Nirvana pm2 logs
sudo -u Nirvana pm2 monit

# Nginx operations
nginx -t
systemctl reload nginx
systemctl restart nginx
```

#### Log Monitoring
```bash
# Application logs
tail -f /var/log/nirvana-test/pm2-*.log

# Nginx logs
tail -f /var/log/nirvana-test/nginx-*.log

# System logs
journalctl -f
```

#### Backup and Recovery
```bash
# Manual backup
sudo -u Nirvana /var/www/nirvana-test/backup.sh

# List backups
ls -la /var/backups/nirvana-organics-test/

# Check backup cron
sudo -u Nirvana crontab -l
```

### File Locations

#### Configuration Files
- **Main Environment**: `/var/www/nirvana-test/.env.test`
- **Admin Environment**: `/var/www/nirvana-test/.env.admin.test`
- **PM2 Ecosystem**: `/var/www/nirvana-test/ecosystem.config.js`
- **Nginx Config**: `/etc/nginx/sites-available/nirvana-organics-test`

#### Application Directories
- **Backend**: `/var/www/nirvana-test/server/`
- **Main Frontend**: `/var/www/nirvana-test/main/`
- **Admin Frontend**: `/var/www/nirvana-test/admin/`
- **Uploads**: `/var/www/nirvana-test/uploads/`

#### Log Directories
- **Application Logs**: `/var/log/nirvana-test/`
- **Nginx Logs**: `/var/log/nginx/`
- **System Logs**: `/var/log/`

### Support and Resources

#### Documentation
- [Node.js Documentation](https://nodejs.org/docs/)
- [PM2 Documentation](https://pm2.keymetrics.io/docs/)
- [Nginx Documentation](https://nginx.org/en/docs/)
- [Let's Encrypt Documentation](https://letsencrypt.org/docs/)

#### Community Support
- [Stack Overflow](https://stackoverflow.com/)
- [GitHub Issues](https://github.com/)
- [Server Fault](https://serverfault.com/)

---

## Conclusion

This comprehensive deployment guide provides everything needed to successfully deploy the Nirvana Organics e-commerce platform on a fresh Hostinger VPS server. The unified deployment script automates the entire process while maintaining flexibility for both test and production environments.

For additional support or questions, please refer to the troubleshooting section or contact the development team.

**Deployment Script Version**: 2.0.0
**Last Updated**: 2025-01-01
**Compatible Environments**: Ubuntu 20.04+, Debian 11+
