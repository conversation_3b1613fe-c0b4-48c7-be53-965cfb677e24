# Square SDK Troubleshooting Guide

## 🚨 **Issue: MODULE_NOT_FOUND Error in Square SDK**

### **Problem Description**
Both the main backend server (port 5000) and admin backend server (port 3001) in the Nirvana Organics test environment are experiencing MODULE_NOT_FOUND errors when trying to load the Square SDK. The servers start successfully but crash when attempting to load `squareService.js`.

### **Root Cause Analysis**
The issue stems from a **dependency version mismatch**:
- **package.json** specifies: `"square": "^39.0.0"`
- **package-lock.json** shows: `"@square/square": "github:square/square-nodejs-sdk"`

This mismatch causes Node.js module resolution to fail when the application tries to `require('square')`.

---

## 🔧 **Immediate Fix (Recommended)**

### **Step 1: Quick Fix Script**
Run the quick fix script to resolve the issue immediately:

```bash
# Connect to your server
ssh root@*************

# Navigate to deployment directory
cd /root/nirvana-deployment

# Make script executable
chmod +x quick-fix-square.sh

# Run quick fix for test environment
./quick-fix-square.sh test
```

### **Step 2: Manual Fix (Alternative)**
If the script is not available, follow these manual steps:

```bash
# Stop PM2 applications
sudo -u Nirvana pm2 stop nirvana-backend-main-test
sudo -u Nirvana pm2 stop nirvana-backend-admin-test

# Navigate to server directory
cd /var/www/nirvana-test/server

# Remove conflicting Square installations
sudo -u Nirvana rm -rf node_modules/square node_modules/@square

# Install correct Square SDK version
sudo -u Nirvana npm install square@39.0.0 --no-save --no-package-lock

# Verify installation
sudo -u Nirvana node -e "
try {
    const { Client, Environment } = require('square');
    console.log('✅ Square SDK loaded successfully');
} catch (error) {
    console.error('❌ Square SDK test failed:', error.message);
}
"

# Test SquareService loading
sudo -u Nirvana node -e "
try {
    const SquareService = require('./services/squareService');
    console.log('✅ SquareService loaded successfully');
} catch (error) {
    console.error('❌ SquareService loading failed:', error.message);
}
"

# Restart PM2 applications
sudo -u Nirvana pm2 start nirvana-backend-main-test
sudo -u Nirvana pm2 start nirvana-backend-admin-test

# Check status
sudo -u Nirvana pm2 status
```

---

## 🔍 **Comprehensive Diagnostic**

### **Step 1: Run Comprehensive Diagnostic Script**
For detailed analysis and advanced troubleshooting:

```bash
# Make script executable
chmod +x fix-square-sdk-issue.sh

# Run comprehensive diagnostic
./fix-square-sdk-issue.sh test
```

### **Step 2: Manual Diagnostic Commands**

#### **Check Package Dependencies**
```bash
cd /var/www/nirvana-test/server

# Check package.json for Square dependency
grep -n "square" package.json

# Check package-lock.json for Square dependency
grep -A 5 -B 5 "square" package-lock.json

# List installed Square packages
ls -la node_modules/ | grep square
```

#### **Check Node.js Module Resolution**
```bash
# Test Square SDK loading
sudo -u Nirvana node -e "
try {
    console.log('Testing Square SDK loading...');
    const square = require('square');
    console.log('✅ Success: Square SDK loaded');
    console.log('Available exports:', Object.keys(square));
} catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Error code:', error.code);
    console.error('Module path:', error.path);
}
"
```

#### **Check PM2 Application Logs**
```bash
# View recent error logs
tail -50 /var/log/nirvana-test/pm2-main-error.log
tail -50 /var/log/nirvana-test/pm2-admin-error.log

# Monitor logs in real-time
sudo -u Nirvana pm2 logs --lines 20
```

---

## 🛠️ **Advanced Troubleshooting**

### **Issue 1: npm Installation Fails**
```bash
# Clear npm cache
sudo -u Nirvana npm cache clean --force

# Remove node_modules completely
sudo -u Nirvana rm -rf node_modules package-lock.json

# Reinstall all dependencies
sudo -u Nirvana npm install --production
```

### **Issue 2: Permission Problems**
```bash
# Fix ownership of server directory
sudo chown -R Nirvana:Nirvana /var/www/nirvana-test/server

# Fix permissions
sudo chmod -R 755 /var/www/nirvana-test/server
sudo chmod -R 644 /var/www/nirvana-test/server/package*.json
```

### **Issue 3: Node.js Version Compatibility**
```bash
# Check Node.js version
node --version

# Square SDK v39.0.0 requires Node.js >= 14.x
# If version is incompatible, update Node.js:
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### **Issue 4: Environment Variables Missing**
```bash
# Check Square environment variables
cd /var/www/nirvana-test
grep -i square .env.test .env.admin.test

# Required variables:
# SQUARE_ACCESS_TOKEN
# SQUARE_ENVIRONMENT (sandbox/production)
# SQUARE_LOCATION_ID
```

---

## 🔄 **Prevention Strategies**

### **1. Lock Dependency Versions**
Update `package.json` to use exact versions:
```json
{
  "dependencies": {
    "square": "39.0.0"
  }
}
```

### **2. Use npm ci for Production**
Replace `npm install` with `npm ci` in deployment scripts:
```bash
sudo -u Nirvana npm ci --production
```

### **3. Regular Dependency Audits**
```bash
# Check for vulnerabilities
sudo -u Nirvana npm audit

# Update dependencies safely
sudo -u Nirvana npm update --save
```

---

## 📊 **Verification Steps**

### **1. Application Health Checks**
```bash
# Test main backend
curl -f http://localhost:5000/health

# Test admin backend  
curl -f http://localhost:3001/health

# Check PM2 status
sudo -u Nirvana pm2 status
```

### **2. Square Service Functionality**
```bash
# Test Square service in Node.js
sudo -u Nirvana node -e "
const SquareService = require('/var/www/nirvana-test/server/services/squareService');
console.log('Square available:', SquareService.isSquareAvailable());
"
```

### **3. Monitor Application Logs**
```bash
# Watch for errors
sudo -u Nirvana pm2 logs --lines 0 --raw | grep -i error

# Check specific application logs
sudo -u Nirvana pm2 logs nirvana-backend-main-test --lines 20
sudo -u Nirvana pm2 logs nirvana-backend-admin-test --lines 20
```

---

## 🆘 **Emergency Recovery**

If all else fails, restore from backup and redeploy:

```bash
# Stop applications
sudo -u Nirvana pm2 stop all

# Restore from backup (if available)
sudo -u Nirvana cp -r /var/www/nirvana-test/server.backup/* /var/www/nirvana-test/server/

# Or redeploy using the unified deployment script
cd /root/nirvana-deployment
./deploy-nirvana-organics.sh test
```

---

## 📞 **Support Information**

### **Log Locations**
- **PM2 Logs**: `/var/log/nirvana-test/pm2-*.log`
- **Application Logs**: `/var/www/nirvana-test/server/logs/`
- **System Logs**: `/var/log/syslog`

### **Key Files**
- **Square Service**: `/var/www/nirvana-test/server/services/squareService.js`
- **Package Config**: `/var/www/nirvana-test/server/package.json`
- **Environment Config**: `/var/www/nirvana-test/.env.test`

### **Useful Commands**
```bash
# PM2 management
sudo -u Nirvana pm2 status
sudo -u Nirvana pm2 restart all
sudo -u Nirvana pm2 logs

# System monitoring
htop
df -h
free -h

# Network testing
netstat -tlnp | grep :5000
netstat -tlnp | grep :3001
```

---

## ✅ **Success Indicators**

After applying the fix, you should see:
- ✅ PM2 applications running without errors
- ✅ Both backends responding to health checks
- ✅ No MODULE_NOT_FOUND errors in logs
- ✅ Square SDK loading successfully in Node.js tests
- ✅ SquareService.isSquareAvailable() returns true (if credentials configured)

The fix should resolve the Square SDK dependency issues and restore full functionality to both the main and admin backend servers.
