# Nirvana Organics - Unified Deployment Solution

## 🚀 Overview

This repository contains a comprehensive, unified deployment solution for the Nirvana Organics e-commerce platform. The deployment system supports both test and production environments on fresh Hostinger VPS servers with a single, automated script.

### ✨ Key Features

- **🔄 Unified Deployment**: Single script handles both test and production environments
- **🏗️ Complete Automation**: Full system setup from fresh VPS to running application
- **🔒 Security First**: SSL certificates, firewall configuration, and security hardening
- **📊 Monitoring**: Built-in health monitoring and verification scripts
- **💾 Backup System**: Automated backup and recovery procedures
- **📚 Comprehensive Documentation**: Detailed guides and troubleshooting

## 📁 Repository Structure

```
nirvana-organics-deployment/
├── deploy-nirvana-organics.sh    # Main deployment script
├── verify-deployment.sh          # Deployment verification script
├── health-monitor.sh             # Health monitoring script
├── DEPLOYMENT_GUIDE.md           # Comprehensive deployment guide
├── QUICK_START.md                # Quick start guide
└── README.md                     # This file
```

## 🎯 Quick Start

### Prerequisites
- Fresh Ubuntu 20.04+ or Debian 11+ VPS server
- Root/sudo access to the server
- DNS configured for your domain(s)
- Source code files ready for deployment

### 1. Upload Files to Server
```bash
# Connect to your server
ssh root@your-server-ip

# Create deployment directory
mkdir -p /root/nirvana-deployment
cd /root/nirvana-deployment

# Upload all deployment files and source code here
```

### 2. Run Deployment
```bash
# Make script executable
chmod +x deploy-nirvana-organics.sh

# Deploy test environment
sudo ./deploy-nirvana-organics.sh test

# Deploy production environment
sudo ./deploy-nirvana-organics.sh production
```

### 3. Verify Deployment
```bash
# Run verification script
chmod +x verify-deployment.sh
./verify-deployment.sh test  # or production
```

## 🏗️ Architecture

### Environment Structure
```
Test Environment (test.shopnirvanaorganics.com):
├── /var/www/nirvana-test/
│   ├── server/          # Backend Node.js application
│   ├── main/            # Main customer frontend
│   ├── admin/           # Admin panel frontend
│   └── uploads/         # File uploads

Production Environment (shopnirvanaorganics.com):
├── /var/www/nirvana-production/
│   ├── server/          # Backend Node.js application
│   ├── main/            # Main customer frontend
│   ├── admin/           # Admin panel frontend
│   └── uploads/         # File uploads
```

### Technology Stack
- **Backend**: Node.js 18.x with Express.js
- **Frontend**: Vite-built React applications
- **Database**: MySQL/MariaDB with Sequelize ORM
- **Web Server**: Nginx with SSL/TLS termination
- **Process Manager**: PM2 for Node.js applications
- **Cache**: Redis for session and data caching
- **SSL**: Let's Encrypt certificates with auto-renewal

## 📋 Deployment Features

### System Setup
- ✅ System package updates and security patches
- ✅ User account creation and permission configuration
- ✅ Firewall setup and security hardening
- ✅ Node.js, PM2, Nginx, MySQL, Redis installation

### Application Deployment
- ✅ Backend code deployment and dependency installation
- ✅ Frontend building and deployment (main + admin)
- ✅ Environment-specific configuration generation
- ✅ Database connection testing and migration

### Service Configuration
- ✅ Nginx virtual host with SSL and security headers
- ✅ PM2 ecosystem configuration with clustering
- ✅ SSL certificate generation and auto-renewal
- ✅ Rate limiting and security policies

### Monitoring & Maintenance
- ✅ Automated backup system with retention policies
- ✅ Health monitoring and alerting
- ✅ Log rotation and management
- ✅ Performance optimization

## 🔧 Available Scripts

### Main Deployment Script
```bash
# Deploy test environment
sudo ./deploy-nirvana-organics.sh test

# Deploy production environment
sudo ./deploy-nirvana-organics.sh production
```

### Verification Script
```bash
# Verify test deployment
./verify-deployment.sh test

# Verify production deployment
./verify-deployment.sh production
```

### Health Monitoring
```bash
# Monitor test environment
./health-monitor.sh test

# Monitor production with email alerts
./health-monitor.sh production --email <EMAIL>
```

## 🌐 Access URLs

### Test Environment
- **Main Site**: https://test.shopnirvanaorganics.com
- **Admin Panel**: https://test.shopnirvanaorganics.com/admin
- **Health Check**: https://test.shopnirvanaorganics.com/health

### Production Environment
- **Main Site**: https://shopnirvanaorganics.com
- **Admin Panel**: https://shopnirvanaorganics.com/admin
- **Health Check**: https://shopnirvanaorganics.com/health

## 📊 Management Commands

### Service Management
```bash
# Check service status
systemctl status nginx mariadb redis-server

# PM2 operations
sudo -u Nirvana pm2 status
sudo -u Nirvana pm2 restart all
sudo -u Nirvana pm2 logs

# Nginx operations
nginx -t
systemctl reload nginx
```

### Backup Operations
```bash
# Manual backup
sudo -u Nirvana /var/www/nirvana-test/backup.sh

# List backups
ls -la /var/backups/nirvana-organics-test/
```

### Log Monitoring
```bash
# Application logs
tail -f /var/log/nirvana-test/pm2-*.log

# Nginx logs
tail -f /var/log/nirvana-test/nginx-*.log

# System logs
journalctl -f
```

## 🔒 Security Features

- **SSL/TLS**: Automatic Let's Encrypt certificates with strong cipher suites
- **Firewall**: UFW configuration with minimal required ports
- **Rate Limiting**: API and admin endpoint protection
- **Security Headers**: HSTS, CSP, XSS protection, and more
- **User Isolation**: Dedicated deployment user with minimal privileges
- **Database Security**: SSL connections and limited user permissions

## 📚 Documentation

- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)**: Comprehensive deployment documentation
- **[QUICK_START.md](QUICK_START.md)**: Quick start guide for immediate deployment

### Documentation Sections
1. **Prerequisites**: System requirements and preparation
2. **Deployment Process**: Step-by-step deployment instructions
3. **Post-Deployment**: Verification and testing procedures
4. **Troubleshooting**: Common issues and solutions
5. **Maintenance**: Backup, updates, and monitoring
6. **Security**: Security considerations and best practices

## 🆘 Troubleshooting

### Common Issues
- **SSL Certificate Issues**: Check DNS propagation and domain accessibility
- **Database Connection**: Verify credentials and network connectivity
- **PM2 Applications**: Check logs and restart applications
- **Frontend Not Loading**: Verify file permissions and Nginx configuration

### Getting Help
1. Check the deployment logs: `/var/log/nirvana-test/deployment-*.log`
2. Run the verification script: `./verify-deployment.sh test`
3. Review the troubleshooting section in `DEPLOYMENT_GUIDE.md`
4. Check application logs: `sudo -u Nirvana pm2 logs`

## 🔄 Updates and Maintenance

### Application Updates
```bash
# Stop applications
sudo -u Nirvana pm2 stop all

# Update code and dependencies
# ... (copy new files, run npm install)

# Restart applications
sudo -u Nirvana pm2 start all
```

### System Updates
```bash
# Update system packages
apt update && apt upgrade -y

# Update Node.js dependencies
npm update -g pm2

# Restart services
systemctl restart nginx
sudo -u Nirvana pm2 restart all
```

## 📈 Performance Monitoring

The deployment includes built-in monitoring for:
- **System Resources**: CPU, memory, disk usage
- **Application Health**: PM2 process status and response times
- **Web Endpoints**: HTTP status codes and response times
- **Database Connectivity**: Connection status and query performance
- **SSL Certificates**: Validity and expiration monitoring

## 🤝 Support

For support and questions:
1. Review the comprehensive documentation
2. Check the troubleshooting guides
3. Examine log files for error details
4. Use the verification script to identify issues

## 📄 License

This deployment solution is part of the Nirvana Organics project.

---

**Version**: 2.0.0  
**Last Updated**: 2025-01-01  
**Compatible Systems**: Ubuntu 20.04+, Debian 11+  
**Node.js Version**: 18.x LTS
