#!/bin/bash

# =============================================================================
# Nirvana Organics - Health Monitoring Script
# =============================================================================
# 
# Continuous health monitoring for Nirvana Organics deployment
# Checks system health and sends alerts when issues are detected
# 
# Usage:
#   ./health-monitor.sh [test|production] [--email <EMAIL>]
# 
# Examples:
#   ./health-monitor.sh test
#   ./health-monitor.sh production --email <EMAIL>
# 
# =============================================================================

set -e

# =============================================================================
# CONFIGURATION
# =============================================================================

ENVIRONMENT="${1:-test}"
EMAIL_ALERT=""
SCRIPT_VERSION="1.0.0"
MONITOR_DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --email)
            EMAIL_ALERT="$2"
            shift 2
            ;;
        test|production)
            ENVIRONMENT="$1"
            shift
            ;;
        *)
            shift
            ;;
    esac
done

# Validate environment
if [[ "$ENVIRONMENT" != "test" && "$ENVIRONMENT" != "production" ]]; then
    echo "❌ Error: Invalid environment '$ENVIRONMENT'"
    echo "Valid environments: test, production"
    exit 1
fi

# Environment-specific configuration
if [[ "$ENVIRONMENT" == "test" ]]; then
    DOMAIN="test.shopnirvanaorganics.com"
    PM2_MAIN_APP="nirvana-backend-main-test"
    PM2_ADMIN_APP="nirvana-backend-admin-test"
    LOG_PATH="/var/log/nirvana-test"
    MAIN_PORT="5000"
    ADMIN_PORT="3001"
else
    DOMAIN="shopnirvanaorganics.com"
    PM2_MAIN_APP="nirvana-backend-main-production"
    PM2_ADMIN_APP="nirvana-backend-admin-production"
    LOG_PATH="/var/log/nirvana-production"
    MAIN_PORT="5000"
    ADMIN_PORT="3001"
fi

DEPLOY_USER="Nirvana"
HEALTH_LOG="$LOG_PATH/health-monitor.log"

# Alert thresholds
CPU_THRESHOLD=80
MEMORY_THRESHOLD=85
DISK_THRESHOLD=90
RESPONSE_TIME_THRESHOLD=5000  # milliseconds

# =============================================================================
# COLORS AND FORMATTING
# =============================================================================

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
BOLD='\033[1m'
NC='\033[0m'

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    echo "[$timestamp] [$level] $message" | tee -a "$HEALTH_LOG"
}

send_alert() {
    local subject="$1"
    local message="$2"
    
    log_message "ALERT" "$subject: $message"
    
    if [[ -n "$EMAIL_ALERT" ]] && command -v mail &> /dev/null; then
        echo "$message" | mail -s "[$ENVIRONMENT] $subject" "$EMAIL_ALERT"
        log_message "INFO" "Alert email sent to $EMAIL_ALERT"
    fi
}

check_command() {
    local cmd="$1"
    if ! command -v "$cmd" &> /dev/null; then
        log_message "ERROR" "Required command '$cmd' not found"
        return 1
    fi
    return 0
}

# =============================================================================
# HEALTH CHECK FUNCTIONS
# =============================================================================

check_system_resources() {
    log_message "INFO" "Checking system resources..."
    
    # Check CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    cpu_usage=${cpu_usage%.*}  # Remove decimal part
    
    if [[ $cpu_usage -gt $CPU_THRESHOLD ]]; then
        send_alert "High CPU Usage" "CPU usage is ${cpu_usage}% (threshold: ${CPU_THRESHOLD}%)"
    else
        log_message "INFO" "CPU usage: ${cpu_usage}% (OK)"
    fi
    
    # Check memory usage
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_mem * 100 / total_mem))
    
    if [[ $memory_usage -gt $MEMORY_THRESHOLD ]]; then
        send_alert "High Memory Usage" "Memory usage is ${memory_usage}% (threshold: ${MEMORY_THRESHOLD}%)"
    else
        log_message "INFO" "Memory usage: ${memory_usage}% (OK)"
    fi
    
    # Check disk usage
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | cut -d'%' -f1)
    
    if [[ $disk_usage -gt $DISK_THRESHOLD ]]; then
        send_alert "High Disk Usage" "Disk usage is ${disk_usage}% (threshold: ${DISK_THRESHOLD}%)"
    else
        log_message "INFO" "Disk usage: ${disk_usage}% (OK)"
    fi
}

check_services() {
    log_message "INFO" "Checking system services..."
    
    # Check Nginx
    if ! systemctl is-active --quiet nginx; then
        send_alert "Service Down" "Nginx service is not running"
    else
        log_message "INFO" "Nginx service: Running (OK)"
    fi
    
    # Check MariaDB
    if ! systemctl is-active --quiet mariadb; then
        send_alert "Service Down" "MariaDB service is not running"
    else
        log_message "INFO" "MariaDB service: Running (OK)"
    fi
    
    # Check Redis (optional)
    if systemctl is-active --quiet redis-server; then
        log_message "INFO" "Redis service: Running (OK)"
    else
        log_message "WARN" "Redis service: Not running (optional)"
    fi
}

check_pm2_applications() {
    log_message "INFO" "Checking PM2 applications..."
    
    # Check main backend
    if sudo -u "$DEPLOY_USER" pm2 describe "$PM2_MAIN_APP" &>/dev/null; then
        local main_status=$(sudo -u "$DEPLOY_USER" pm2 describe "$PM2_MAIN_APP" | grep "status" | awk '{print $4}' | head -1)
        if [[ "$main_status" != "online" ]]; then
            send_alert "Application Down" "Main backend status: $main_status"
        else
            log_message "INFO" "Main backend: Online (OK)"
        fi
    else
        send_alert "Application Missing" "Main backend application not found"
    fi
    
    # Check admin backend
    if sudo -u "$DEPLOY_USER" pm2 describe "$PM2_ADMIN_APP" &>/dev/null; then
        local admin_status=$(sudo -u "$DEPLOY_USER" pm2 describe "$PM2_ADMIN_APP" | grep "status" | awk '{print $4}' | head -1)
        if [[ "$admin_status" != "online" ]]; then
            send_alert "Application Down" "Admin backend status: $admin_status"
        else
            log_message "INFO" "Admin backend: Online (OK)"
        fi
    else
        send_alert "Application Missing" "Admin backend application not found"
    fi
}

check_database_connectivity() {
    log_message "INFO" "Checking database connectivity..."
    
    if mysql -h srv1921.hstgr.io -P 3306 -u u106832845_root -pHrishikesh@0912 -e "SELECT 1;" &>/dev/null; then
        log_message "INFO" "Database connectivity: OK"
    else
        send_alert "Database Error" "Cannot connect to database server"
    fi
}

check_web_endpoints() {
    log_message "INFO" "Checking web endpoints..."
    
    # Check main site
    local start_time=$(date +%s%3N)
    local main_response=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "https://$DOMAIN/" 2>/dev/null || echo "000")
    local end_time=$(date +%s%3N)
    local response_time=$((end_time - start_time))
    
    if [[ "$main_response" != "200" ]]; then
        send_alert "Website Down" "Main site returns HTTP $main_response"
    elif [[ $response_time -gt $RESPONSE_TIME_THRESHOLD ]]; then
        send_alert "Slow Response" "Main site response time: ${response_time}ms (threshold: ${RESPONSE_TIME_THRESHOLD}ms)"
    else
        log_message "INFO" "Main site: HTTP $main_response, ${response_time}ms (OK)"
    fi
    
    # Check admin panel
    start_time=$(date +%s%3N)
    local admin_response=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "https://$DOMAIN/admin/" 2>/dev/null || echo "000")
    end_time=$(date +%s%3N)
    response_time=$((end_time - start_time))
    
    if [[ "$admin_response" != "200" ]]; then
        send_alert "Admin Panel Down" "Admin panel returns HTTP $admin_response"
    elif [[ $response_time -gt $RESPONSE_TIME_THRESHOLD ]]; then
        send_alert "Slow Response" "Admin panel response time: ${response_time}ms"
    else
        log_message "INFO" "Admin panel: HTTP $admin_response, ${response_time}ms (OK)"
    fi
    
    # Check health endpoint
    local health_response=$(curl -s -o /dev/null -w "%{http_code}" --max-time 5 "https://$DOMAIN/health" 2>/dev/null || echo "000")
    if [[ "$health_response" != "200" ]]; then
        send_alert "Health Check Failed" "Health endpoint returns HTTP $health_response"
    else
        log_message "INFO" "Health endpoint: HTTP $health_response (OK)"
    fi
}

check_ssl_certificate() {
    log_message "INFO" "Checking SSL certificate..."
    
    if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" </dev/null 2>/dev/null | openssl x509 -noout -checkend 604800 &>/dev/null; then
        log_message "INFO" "SSL certificate: Valid (OK)"
    else
        # Check if certificate expires within 7 days
        if openssl s_client -connect "$DOMAIN:443" -servername "$DOMAIN" </dev/null 2>/dev/null | openssl x509 -noout -checkend 0 &>/dev/null; then
            send_alert "SSL Certificate Expiring" "SSL certificate for $DOMAIN expires within 7 days"
        else
            send_alert "SSL Certificate Invalid" "SSL certificate for $DOMAIN is invalid or expired"
        fi
    fi
}

check_log_errors() {
    log_message "INFO" "Checking for recent errors in logs..."
    
    # Check PM2 logs for errors in the last 5 minutes
    local error_count=0
    
    if [[ -f "$LOG_PATH/pm2-main-error.log" ]]; then
        local recent_errors=$(find "$LOG_PATH/pm2-main-error.log" -mmin -5 -exec wc -l {} \; 2>/dev/null | awk '{sum+=$1} END {print sum+0}')
        error_count=$((error_count + recent_errors))
    fi
    
    if [[ -f "$LOG_PATH/pm2-admin-error.log" ]]; then
        local recent_errors=$(find "$LOG_PATH/pm2-admin-error.log" -mmin -5 -exec wc -l {} \; 2>/dev/null | awk '{sum+=$1} END {print sum+0}')
        error_count=$((error_count + recent_errors))
    fi
    
    if [[ $error_count -gt 10 ]]; then
        send_alert "High Error Rate" "Found $error_count errors in logs in the last 5 minutes"
    else
        log_message "INFO" "Error logs: $error_count recent errors (OK)"
    fi
}

check_backup_status() {
    log_message "INFO" "Checking backup status..."
    
    local backup_dir="/var/backups/nirvana-organics-$ENVIRONMENT"
    if [[ -d "$backup_dir" ]]; then
        local latest_backup=$(ls -t "$backup_dir"/*.tar.gz 2>/dev/null | head -1)
        if [[ -n "$latest_backup" ]]; then
            local backup_age=$((($(date +%s) - $(stat -c %Y "$latest_backup")) / 86400))
            if [[ $backup_age -gt 2 ]]; then
                send_alert "Backup Warning" "Latest backup is $backup_age days old"
            else
                log_message "INFO" "Backup status: Latest backup is $backup_age days old (OK)"
            fi
        else
            send_alert "Backup Missing" "No backup files found in $backup_dir"
        fi
    else
        send_alert "Backup Directory Missing" "Backup directory $backup_dir not found"
    fi
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

print_header() {
    echo -e "\n${WHITE}${BOLD}"
    echo "================================================================================"
    echo "                    NIRVANA ORGANICS HEALTH MONITOR"
    echo "================================================================================"
    echo -e "${NC}"
    echo -e "Environment: ${CYAN}${BOLD}$ENVIRONMENT${NC}"
    echo -e "Domain: ${CYAN}${BOLD}$DOMAIN${NC}"
    echo -e "Monitor Date: ${CYAN}${BOLD}$MONITOR_DATE${NC}"
    echo -e "Script Version: ${CYAN}${BOLD}$SCRIPT_VERSION${NC}"
    if [[ -n "$EMAIL_ALERT" ]]; then
        echo -e "Email Alerts: ${CYAN}${BOLD}$EMAIL_ALERT${NC}"
    fi
    echo -e "\n${WHITE}$(printf '=%.0s' {1..80})${NC}\n"
}

main() {
    print_header
    
    # Ensure log directory exists
    mkdir -p "$LOG_PATH"
    
    log_message "INFO" "Starting health monitoring for $ENVIRONMENT environment"
    
    # Run all health checks
    check_system_resources
    check_services
    check_pm2_applications
    check_database_connectivity
    check_web_endpoints
    check_ssl_certificate
    check_log_errors
    check_backup_status
    
    log_message "INFO" "Health monitoring completed"
    
    echo -e "\n${GREEN}${BOLD}✅ Health monitoring completed successfully!${NC}"
    echo -e "${CYAN}Check the log file for details: $HEALTH_LOG${NC}\n"
}

# Show usage if no arguments provided
if [[ $# -eq 0 ]]; then
    echo "Usage: $0 [test|production] [--email <EMAIL>]"
    echo ""
    echo "Examples:"
    echo "  $0 test"
    echo "  $0 production --email <EMAIL>"
    echo ""
    echo "This script performs comprehensive health checks on the Nirvana Organics deployment."
    exit 1
fi

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
