# =============================================================================
# Nginx Virtual Host Configuration - Test Environment
# =============================================================================
# 
# Domain: test.shopnirvanaorganics.com
# Environment: Test
# Backend Ports: 5000 (main), 3001 (admin)
# Document Roots: /var/www/nirvana-test/main/ (main), /var/www/nirvana-test/admin/ (admin)
# 
# Installation:
#   1. Copy this file to /etc/nginx/sites-available/nirvana-organics-test
#   2. Create symlink: ln -s /etc/nginx/sites-available/nirvana-organics-test /etc/nginx/sites-enabled/
#   3. Test configuration: nginx -t
#   4. Reload Nginx: systemctl reload nginx
# 
# =============================================================================

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=general:10m rate=50r/s;
limit_req_zone $binary_remote_addr zone=api:10m rate=20r/s;
limit_req_zone $binary_remote_addr zone=admin:10m rate=10r/s;

# Upstream backend servers
upstream nirvana_backend_main_test {
    server 127.0.0.1:5000 fail_timeout=5s max_fails=3;
    keepalive 32;
}

upstream nirvana_backend_admin_test {
    server 127.0.0.1:3001 fail_timeout=5s max_fails=3;
    keepalive 32;
}

# HTTP to HTTPS redirect
server {
    listen 80;
    listen [::]:80;
    server_name test.shopnirvanaorganics.com;
    
    # Let's Encrypt challenge location
    location /.well-known/acme-challenge/ {
        root /var/www/html;
        try_files $uri =404;
    }
    
    # Redirect all other HTTP traffic to HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# Main HTTPS server block
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name test.shopnirvanaorganics.com;
    
    # Document root for static files
    root /var/www/nirvana-test/main;
    index index.html index.htm;
    
    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/test.shopnirvanaorganics.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/test.shopnirvanaorganics.com/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/test.shopnirvanaorganics.com/chain.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=********; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://js.squareup.com https://js.squareupsandbox.com https://apis.google.com https://accounts.google.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://squareupsandbox.com https://connect.squareupsandbox.com https://accounts.google.com; frame-src https://js.squareup.com https://js.squareupsandbox.com https://accounts.google.com;" always;
    
    # Logging
    access_log /var/log/nirvana-test/nginx-access.log combined;
    error_log /var/log/nirvana-test/nginx-error.log warn;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # Rate limiting
    limit_req zone=general burst=100 nodelay;
    
    # Admin Panel - Higher security, lower rate limits
    location /admin/ {
        alias /var/www/nirvana-test/admin/;
        try_files $uri $uri/ /admin/index.html;
        
        # Admin-specific rate limiting
        limit_req zone=admin burst=20 nodelay;
        
        # Additional security headers for admin
        add_header X-Robots-Tag "noindex, nofollow, nosnippet, noarchive" always;
        
        # Static file caching for admin assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Robots-Tag "noindex, nofollow, nosnippet, noarchive" always;
        }
    }
    
    # Admin API endpoints
    location /admin/api/ {
        limit_req zone=admin burst=10 nodelay;
        
        proxy_pass http://nirvana_backend_admin_test/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # Admin API security headers
        add_header X-Robots-Tag "noindex, nofollow, nosnippet, noarchive" always;
    }
    
    # Main API endpoints
    location /api/ {
        limit_req zone=api burst=50 nodelay;
        
        proxy_pass http://nirvana_backend_main_test/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://nirvana_backend_main_test/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        access_log off;
    }
    
    # File uploads
    location /uploads/ {
        alias /var/www/nirvana-test/uploads/;
        expires 1y;
        add_header Cache-Control "public";
        
        # Security for uploaded files
        location ~* \.(php|php5|phtml|pl|py|jsp|asp|sh|cgi)$ {
            deny all;
        }
    }
    
    # Static file caching for main site
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # Main site - Single Page Application
    location / {
        try_files $uri $uri/ /index.html;
        
        # Prevent caching of index.html
        location = /index.html {
            expires -1;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
        }
    }
    
    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~* \.(env|log|htaccess|htpasswd|ini|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Custom error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        root /var/www/nirvana-test/main;
        internal;
    }
    
    location = /50x.html {
        root /var/www/nirvana-test/main;
        internal;
    }
    
    # Maintenance mode (uncomment to enable)
    # location / {
    #     return 503;
    # }
    # 
    # location = /maintenance.html {
    #     root /var/www/nirvana-test/main;
    #     internal;
    # }
    # 
    # error_page 503 /maintenance.html;
}

# Nginx status page (for monitoring)
server {
    listen 127.0.0.1:8080;
    server_name localhost;
    
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }
}
